<template>
    <div
        class="form-check form-switch"
        :class="{'is-invalid': isInvalid}"
    >
        <input
            :id="id"
            type="checkbox"
            class="form-check-input"
            :disabled="!enabled"
            :checked="checked"
            :class="{'is-invalid': isInvalid}"
            @input="onInput"
        >
    </div>
</template>

<script lang="ts" setup>
import { FormComponentCommon } from '@/types';
import { computed } from 'vue'

const $props = withDefaults(defineProps<FormComponentCommon & {
    value: null | number | boolean | string
}>(), {
    enabled: true,
    value: false,
})

const $emit = defineEmits<{
    change: [value: 0 | 1]
}>()

const checked = computed(() => {
    return $props.value === true || $props.value === '1' || $props.value === 1;
})

function onInput (e: Event) {
    $emit('change', (e.target as HTMLInputElement).checked ? 1 : 0)
}
</script>
