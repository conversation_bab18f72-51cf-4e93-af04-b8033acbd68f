<template>
    <rich-table
        v-bind="richTableProps"
        @reload="onReload"
    >
        <template
            v-if="allowAdd"
            #afterTitle="{refreshCallback}"
        >
            <popover
                hideOnOutside
                :isOpened="isOpened"
            >
                <button
                    class="btn btn-sm btn-success"
                    type="button"
                    @click="showAddForm"
                >
                    <icona name="icn-plus" /> Add bonus offer
                </button>
                <template #content>
                    <form-grid
                        v-bind="form"
                        @change="form.values = $event"
                        @submit="submit($event, refreshCallback)"
                    />
                </template>
            </popover>
        </template>

        <template #actions="{row, refreshCallback}">
            <button
                v-if="row.allowCancel"
                type="button"
                class="btn btn-xs btn-danger"
                @click="cancelOffer(row, refreshCallback)"
            >
                <icona name="icn-delete" />
            </button>
            <button
                v-if="row.allowDeactivate"
                type="button"
                class="btn btn-xs btn-warning"
                @click="deactivateOffer(row, refreshCallback)"
            >
                <icona name="icn-toggle-left" />
            </button>
        </template>
    </rich-table>
</template>

<script lang="ts">

import { FormGrid, Icona, Popover } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, RichTableType, TableRow, Values } from '@/types'
import CommonTable from './common-table.vue'

interface BonusOffersResponse extends RichTableType {
    allowAdd: boolean
}

export default defineComponent({
    components: {
        Icona,
        Popover,
        FormGrid,
    },
    mixins: [
        CommonTable,
    ],
    data () {
        return {
            form: {} as FormGridType,
            isOpened: false,
            allowAdd: false,
        }
    },
    watch: {
        loadPromise: {
            immediate: true,
            async handler (loadPromise: Promise<BonusOffersResponse>) {
                const data = await loadPromise
                this.allowAdd = data.allowAdd
            },
        },
    },
    methods: {
        async showAddForm () {
            this.form = await this.$fetch('/user/player/bonus-offers/add-form', { siteId: this.siteIdUserId.siteId })
        },

        cancelOffer (row: TableRow, refreshCallback: () => void) {
            if (confirm('Really?')) {
                return this.$fetch('/user/player/bonus-offers/cancel', Object.assign({ bonusOfferId: row.id }, this.siteIdUserId))
                    .then(refreshCallback)
            }
        },

        deactivateOffer (row: TableRow, refreshCallback: () => void) {
            if (confirm('Really?')) {
                return this.$fetch('/user/player/bonus-offers/deactivate', Object.assign({ bonusOfferId: row.id }, this.siteIdUserId))
                    .then(refreshCallback)
            }
        },

        submit (values: Values, refreshCallback: () => void) {
            this.$processFormResponse(this.$fetch('/user/player/bonus-offers/add', Object.assign({}, values, this.siteIdUserId)), this.form).then(() => {
                this.isOpened = true
                this.$nextTick(() => {
                    this.isOpened = false
                })
                refreshCallback()
            })
        },
    },
})
</script>
