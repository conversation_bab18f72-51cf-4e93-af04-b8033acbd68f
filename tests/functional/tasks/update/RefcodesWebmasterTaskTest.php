<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\modules\task\actions\update\RefcodesWebmasterTask;
use app\back\repositories\Refcodes;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(RefcodesWebmasterTask::class)]
class RefcodesWebmasterTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public static function refcodeDataProvider(): array
    {
        return [
            ['vp_w40817c136605l13882gplp1225_B10s1vozpPOcwarY-B3BYiF0CEig8', '40817'],
            ['aff_023fd5_34_GooglePlay.30.11', '023fd5'],
            ['vip104562_cstr-43007-48dac406', null],
            ['mb_BQBxJwAAPGQAAEAfAAA.2023-04.24.22149', null],
            ['CP22_1_casinofreak', null],
        ];
    }

    #[DataProvider('refcodeDataProvider')]
    public function testUpdateWebmasterId(string $code, ?string $expectedWebmasterId): void
    {
        $this->haveRates();
        $this->haveRefcodeRecord(['code' => $code]);

        $this->runTask('update-refcodes-webmaster', Res::DEFAULT);

        $this->seeRecordWithFields(
            Refcodes::class,
            ['code' => $code],
            ['webmaster_id' => $expectedWebmasterId]
        );
    }
}
