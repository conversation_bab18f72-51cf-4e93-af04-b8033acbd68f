<?php

declare(strict_types=1);

namespace app\tests\unit\modules\reports;

use app\back\entities\enums\WpAffOwner;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\repositories\Refcodes;
use app\back\repositories\WpWebmasters;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\TestInlineReportConfig;
use Monolog\Test\TestCase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(WebmasterAffOwnerGroupColumn::class)]
class WebmasterAffOwnerGroupColumnTest extends TestCase
{
    use DbTransactionalUnitTrait;

    private const string WP_AFF_OWNER_WP = 'WP';

    #[DataProvider('affOwnerGroupsDataProvider')]
    public function testReportWithAffOwnerGroup(string $refcode, bool $needRefcodeAlias, ?string $affOwner, ?int $expectedAffOwnerGroup): void
    {
        $this->haveWpAffOwnerGroupRecord(['name' => self::WP_AFF_OWNER_WP, 'group' => WpAffOwner::GROUP_WP]);
        $this->haveWpAffOwnerGroupRecord(['name' => WpAffOwner::VIPAFF_FAKE_OWNER, 'group' => WpAffOwner::GROUP_VIPAFF]);

        $webmaster = $affOwner ? $this->haveWpWebmasterRecord(['aff_owner' => $affOwner]) : (object)['id' => null];

        $this->haveRecord(Refcodes::class, ['code' => $refcode, 'webmaster_id' => $webmaster?->id]);

        $report = TestInlineReportConfig::object(
            $this->container(),
            columns: [
                'refcode' => [RefcodeColumn::class, 'r'],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => $needRefcodeAlias ? 'r' : null],
            ],
            tableMap: [
                'r' => [Refcodes::TABLE_NAME],
                'wp_w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w', 'r'), ['r']],
            ]
        );

        $report->loadAndValidateOrException([
            'columns' => ['refcode', 'aff_owner_group'],
        ]);

        self::assertSame([[
            'refcode' => $refcode,
            'aff_owner_group' => $expectedAffOwnerGroup ? WPAffOwner::GROUPS[$expectedAffOwnerGroup] : null,
        ]], $report->getData());
    }

    public static function affOwnerGroupsDataProvider(): array
    {
        return [
            ['vip100744miniapp98', true, null, WpAffOwner::GROUP_VIPAFF],
            ['vip100744miniapp98', true, self::WP_AFF_OWNER_WP, WpAffOwner::GROUP_VIPAFF],
            ['vip100744miniapp98', false, self::WP_AFF_OWNER_WP, null],
            ['vip100744miniapp98', false, null, null],
            ['CP22_1_casinofreak', true, null, WpAffOwner::GROUP_VIPAFF],
            ['wp_w51306p43_4949-androidoutsource_reg', true, self::WP_AFF_OWNER_WP, WPAffOwner::GROUP_WP],
            ['wp_w51306p43_4949-androidoutsource_reg', false, self::WP_AFF_OWNER_WP, WPAffOwner::GROUP_WP],
            ['wp_w51306p43_4949-androidoutsource_reg', false, null, null],
        ];
    }
}
