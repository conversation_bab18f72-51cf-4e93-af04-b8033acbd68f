<?php

declare(strict_types=1);

use app\back\config\tasks\Res;

$vipaff = [
    'vipaff-refcodes' => [
        'class' => \app\back\modules\task\actions\VipaffRefcodesTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\UniversalRequest::class,
            'url' => 'api/analytics/refcodes/',
            'parserConfig' => \app\back\components\parsers\CsvParser::class,
            'aliases' => [
                'ref_code' => 'ref_code',
                'webmaster_id' => 'user_id',
                'prog_type' => 'prog_type',
                'aff_source' => 'traffic_source',
                'updated_at' => 'updated',
            ],
        ],
    ],
    'vipaff-comments' => [
        'class' => \app\back\modules\task\actions\VipaffCommentsTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\UniversalRequest::class,
            'url' => 'api/analytics/customers/',
            'parserConfig' => \app\back\components\parsers\CsvParser::class,
            'aliases' => [
                'user_id' => 'uid',
                'site_id' => 'site_id',
                'comment' => 'comment',
                'created_at' => 'updated',
                'updated_at' => 'updated',
            ],
        ],
    ],
    'vipaff-users' => [
        'class' => \app\back\modules\task\actions\VipaffUsersTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\UniversalRequest::class,
            'url' => 'api/analytics/customers/',
            'parserConfig' => \app\back\components\parsers\CsvParser::class,
            'aliases' => [
                'user_id' => 'uid',
                'site_id' => 'site_id',
                'is_aff_hidden' => 'is_hid',
                'updated_at' => 'updated',
            ],
        ],
    ],
];

return [
    Res::VIPAFF => $vipaff,
    Res::VIPAFF_CP => $vipaff,
];
