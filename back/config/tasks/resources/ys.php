<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\modules\task\requests\YsDocumentsSumSubHeadersRequest;
use app\back\modules\task\requests\YsRequest;

/*
'achievements',
'achievementsCategories',
'achievementsChains',
'bettingEvents', *
'bettingEventsItem',
'bettingEventsNew',
'bettingEventsSport',
'bettingEventsSportCategory',
'bonusesActivations', *
'bonusesActivationsPayments',
'bonusesConditionTypes',
'bonusesConditions',
'bonusesConditionsGames',
'bonusesIncomesPrizes', *
'cashBackCalculation',
'emailValidateLog',
'freezingUsers',
'gamblingLimitsHistory',
'gameBigWins',
'gameEvents',
'gameEventsDaily',
'gameProviders',
'gameTypes',
'games', // swoop
'hhsErrors',
'hhsGameEvents',
'ignoredUsers',
'lotteries',
'lotteriesResults',
'lotteriesTickets',
'loyaltyStatusesStatistics',
'manuals',
'mobileApplication',
'mobileApplicationClient',
'mobileApplicationsDownloads',
'paymentsContexts',
'paymentsProviderInfo',
'prizeTypes',
'prizesSettings',
'shopItemsPurchases',
'tournamentStandings',
'tournaments',
'trackCodeClicks',
'trackCodeClicksRaw',
'transactions',
'unfinishedUsers',
'userParams', *
'userRebillTrackingData',
'usersAchievements',
'usersBlocked',
'usersComment',*
'usersExtended',
'usersFingerprint', *
'usersLanguageLog',
'usersPersonalInfo', *
'usersVipTracking', *
'walletsDailyStatistics',
'walletsEvents', *
'wheelOfFortuneRolls', *
'wheelsOfMoneyDrainUserActions',
'withdrawalsFingerprint',
'withoutUsersLogins',
 */
$tasks = [
    ... require __DIR__ . '/all_sites.php',
    'users' => [
        'class' => \app\back\modules\task\actions\UsersTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/usersExtended',
            'cryptPerLine' => true,
            'aliases' => [
                'user_id' => 'id',
                'ip' => 'ip',
                'email' => 'email',
                'email_confirm' => 'emailConfirmed',
                'phone' => 'phone',
                'phone_confirm' => 'phoneConfirmed',
                'login' => 'login',
                'social' => 'social',
                'locale' => 'languageCode',// locale
                'birthday' => 'birthday',
                'date' => 'dateCreated',
                'date_updated' => 'dateUpdated',
                'last_login' => 'dateLastLogin',
                'ref_code' => 'regCode',
                'reg_uri' => 'regUri',
                'currency' => 'currency',
                'is_ignore' => 'is_test',
                'name' => 'first_name',
                'last_name' => 'last_name',
                'gender' => 'gender',
                'is_blocked' => 'blocked',
                'kyc_verified_at' => 'verifiedAt',
            ],
        ],
    ],
    'users-documents' => [
        'class' => \app\back\modules\task\actions\UsersDocumentsYsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'cryptPerLine' => true,
            'url' => 'analytics/paymentDocumentsRequestFiles',
            'aliases' => [
                'external_id' => 'id',
                'user_id' => 'userId',
                'external_status' => 'status',
                'status_name' => 'statusName',
                'external_created_at' => 'fileCreatedAt',
                'external_updated_at' => 'updatedAt',
                'download_url' => 'url',
            ],
        ],
    ],
    'users-documents-sum-sub' => [
        'class' => \app\back\modules\task\actions\UsersDocumentsSumSubTask::class,
        'request' => [
            'class' => YsRequest::class,
            'cryptPerLine' => true,
            'url' => 'analytics/paymentDocumentRequests',
            'aliases' => [
                'request_id' => 'id',
                'user_id' => 'user_id',
                'type' => 'type',
                'external_status' => 'status',
                'external_created_at' => 'created_at',
                'external_updated_at' => 'updated_at',
            ],
        ],
        'additionalRequests' => [
            \app\back\modules\task\actions\UsersDocumentsSumSubTask::ADDITIONAL_REQUEST_YS_SIGNATURE => [
                'class' => YsDocumentsSumSubHeadersRequest::class,
                'url' => 'analytics/generate-sumsub-image-upload-headers',
                'aliases' => [
                    'url' => 'url',
                    'token' => 'token',
                    'ts' => 'ts',
                    'sig' => 'sig',
                ],
            ],
            \app\back\modules\task\actions\UsersDocumentsSumSubTask::ADDITIONAL_REQUEST_SUM_SUB_RESOURCES => [
                'class' => \app\back\modules\task\requests\SumSubRequest::class,
            ],
        ],
    ],
    'users-stats' => [
        'class' => \app\back\modules\task\actions\UsersTransactionsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/transactions',
            'cryptPerLine' => true,
            'aliases' => [
                'transaction_id' => 'id',
                'remote_id' => 'transaction_id',
                'user_id' => 'userId',
                'ys_op_id' => 'opId',
                'ys_status' => 'status',
                'pay_sys' => 'paySys',
                'wallet' => 'requisite',
                'amount_orig' => 'sum',
                'currency' => 'currency',
                'created_at' => 'dateCreated',
                'sent_at' => 'dateSent',
                'updated_at' => 'dateUpdated',
                'balance_after_changes' => 'balanceAfterChanges',
                'comment' => 'comment',
                'comment_admin' => 'admin_comment',
                'ext_pay_sys' => 'payProvider',
                'is_fake' => 'is_fake',
            ],
        ],
    ],
    'users-stats-win-back-ys' => [
        'class' => \app\back\modules\task\actions\UsersStatsWinBackYsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/walletsWagers',
            'cryptPerLine' => true,
            'aliases' => [
                'id' => 'id',
                'user_id' => 'userId',
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
                'status' => 'status',
                'event_type' => 'type',
                'to_wallet_id' => 'walletId',
                'currency' => 'currency',
                'amount_orig' => 'amount',
            ],
        ],
    ],
    'users-stats-contexts' => [
        'class' => \app\back\modules\task\actions\UsersStatsContextsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/paymentsContexts',
            'cryptPerLine' => true,
            'aliases' => [
                'transaction_id' => 'payment_id',
                //'click_source' => 'source',
                'refcode' => 'login_track_code',
            ],
        ],
    ],
    'users-wallets-real' => [
        'class' => \app\back\modules\task\actions\UsersWalletsRealTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/balances',
            'aliases' => [
                'user_id'   => 'userId',
                'wallet_id' => 'userId',
                'balance' => 'balanceOrig',
                'updated_at' => 'dateChanged',
                'currency' => 'currency',
            ],
        ],
    ],
    'users-wallets-bonus' => [
        'class' => \app\back\modules\task\actions\UsersWalletsBonusTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/balances',
            'aliases' => [
                'user_id'   => 'userId',
                'wallet_id' => 'userId',
                'balance' => 'bonusNotRefundSum',
                'updated_at' => 'dateChanged',
                'currency' => 'currency',
            ],
        ],
    ],
    'users-logins' => [
        'class' => \app\back\modules\task\actions\UsersLoginsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/trackCodeLogins',
            'curlTimeout' => 600,
            'cryptPerLine' => true,
            'aliases' => [
                'user_id' => 'user_id',
                'date' => 'datetime',
                'ip' => 'ip',
                'useragent' => 'user_agent',
                'code' => 'refcode',
            ],
        ],
    ],
    'games-sources-updated' => [
        'class' => \app\back\modules\task\actions\GamesSourcesUpdatedTask::class,
        'gamesSource' => \app\back\entities\GameSource::SOURCE_YS,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/games',
            'cryptPerLine' => true,
            'aliases' => [
                'game_name' => 'name',
                'developer_id' => 'developer_id',
                'game_code' => 'id',
                'is_mobile' => 'is_mobile',
            ],
        ],
    ],
    'game-vendors' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/gameDevelopers',
            'cryptPerLine' => true,
            'aliases' => [
                'id' => 'id',
                'name' => 'name',
            ],
        ],
    ],
    'users-games-raw' => [
/*
id,provider,token,action,parent_id,external_id,external_related_id,user_id,game_id,balance,amount,bonus_amount,platform,currency,created_at,game_session_id,real_balance_after,bonus_balance_after,bonus_wager_sum_after
12537483740,4,b504ff8765128f760ed80e103af590f6,2,12537483670,2112141531190500026,,3861023,12423,14.28,0.00,0.00,2,EUR,"2021-12-14 15:31:20",2112141531190500026,14.28,0.00,0.00
 */
        'class' => \app\back\modules\task\actions\UsersGamesRawTask::class,
        'gamesSource' => \app\back\entities\GameSource::SOURCE_YS,
        'dateFormat' => \app\back\components\helpers\DateHelper::DATETIME_FORMAT_PHP,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/gameEvents',
            'curlTimeout' => 600,
            'cryptPerLine' => true,
            'aliases' => [
                'spin_id' => 'id',
                'user_id' => 'user_id',
                'type' => 'action',
                'session_token' => 'token',
                'provider_id' => 'provider',
                'game_code' => 'game_id',
                'round_id' => 'parent_id',
                'action_id' => 'game_session_id',
                'currency' => 'currency',
                'real_amount' => 'amount',
                'bonus_amount' => 'bonus_amount',
                'real_amount_after' => 'real_balance_after',
                'bonus_amount_after' => 'bonus_balance_after',
                'created_at' => 'created_at',
            ],
        ],
    ],
    'users-games-bets' => [
        'class' => \app\back\modules\task\actions\import\UsersGamesBetsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'cryptPerLine' => true,
            'url' => 'analytics/bettingEvents',
            'aliases' => [
                'bet_id'  => 'id',
                'created_at' => 'date',
                'last_action_at' => 'date_update',
                'status_name' => 'status',
                'user_id' => 'user_id',
                'currency' => 'currency',
                'bet_amount' => 'bet_amount',
                'win_amount' => 'win_amount',
                'is_free_bet' => 'is_free_bet',
            ],
        ],
    ],
    'visits-log-ys' => [
        'class' => \app\back\modules\task\actions\VisitsLogTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/clicksRaw',
            'curlTimeout' => 600,
            'cryptPerLine' => true,
            'aliases' => [
                'created_at' => 'datetime',
                'click_hash' => 'click_hash',
                'user_hash' => 'user_hash',
                'code' => 'refcode',
            ],
        ],
    ],
    'users-blocks' => [
        'class' => \app\back\modules\task\actions\UsersBlocksYsTask::class,
        'request' => [
            'class' => YsRequest::class,
            'url' => 'analytics/usersStatusTransitions',
            'cryptPerLine' => true,
            'aliases' => [
                'user_id' => 'user_id',
                'updated_by_user_id' => 'employee_id',
                'comment' => 'reason',
                'updated_at' => 'created_at',
                'status' => 'status',
            ],
        ],
    ],
    'update-affiliates-codes-stats-from-visits-log' => \app\back\modules\task\actions\update\AffiliatesCodesStatsFromVisitsLogTask::class,
    'update-users-games-aggregate' => \app\back\modules\task\actions\update\UsersGamesAggregateYsTask::class,
    'update-users-max-win' => \app\back\modules\task\actions\update\UsersMaxWinTask::class,
];

$tasksRg = $tasks;
$tasksRg['game-vendors']['request']['cryptPerLine'] = false;
unset($tasksRg['users-documents-sum-sub']);

$tasksSs = $tasks;
$tasksSs['users-games-raw']['fakeSessionTokenForDay'] = true;
$tasksSs['update-users-games-aggregate'] = \app\back\modules\task\actions\update\UsersGamesAggregateYsSsTask::class;

// Tasks configs per resource (site)
return [
    Res::PLATFORM_YS => $tasks,
    Res::PLATFORM_YS_SOFTSWISS => $tasksSs,
    Res::PLATFORM_YS_RG => $tasksRg,
];
