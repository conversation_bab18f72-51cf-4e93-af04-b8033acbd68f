<?php

declare(strict_types=1);

use app\back\components\parsers\JsonParser;
use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\modules\task\requests\SmenFromToRequest;
use app\back\modules\task\requests\SmenRequest;

$tasks = [
    ... require __DIR__ . '/all_sites.php',
    'users' => [
        'class' => \app\back\modules\task\actions\UsersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users',
            'aliases' => [
                'user_id' => 'id',
                'ip' => 'ip',
                'email' => 'email',
                'email_confirm' => 'emailConfirmed',
                'phone' => 'phone',
                'phone_confirm' => 'phoneConfirmed',
                'phone_confirmed_at' => 'phoneConfirmedAt',
                'login' => 'login',
                'social' => 'social',
                'locale' => 'locale',
                'birthday' => 'birthday',
                'date' => 'dateCreated',
                'date_updated' => 'dateUpdated',
                'email_confirmed_at' => 'dateEmailConfirmed',
                'ref_code' => 'refCode',
                'is_ignore' => 'isDeveloper',
                'gender' => 'gender',
                'enabled' => 'enabled',
                'is_toxic' => 'isToxic',
                'registration_method' => 'registrationMethod',
                'uuid' => 'uuid',
                'useragent' => 'userAgent',
                'host' => 'registrationDomain',
                'brand_name' => 'brandName',
                'original_brand_id' => 'brandId',
                'landing_page' => 'landingPageUrl',
            ],
        ],
    ],
    'users-special-info' => [
        'class' => \app\back\modules\task\actions\UsersSpecialInfoTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users',
            'aliases' => [
                'user_id' => 'id',
                'cpf_number' => 'cpfNumber',
            ],
        ],
    ],
    'users-stats' => [
        'class' => \app\back\modules\task\actions\UsersTransactionsTask::class,
        'skipBonuses' => true,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/transactions',
            'aliases' => [
                'transaction_id' => 'id',
                'user_id' => 'userId',
                'type' => 'type',
                'op_id' => 'opId',
                'status' => 'status',
                'pay_sys' => 'paySys',
                'wallet' => 'requisite',
                'amount_orig' => 'sum',
                'currency' => 'currency',
                'created_at' => 'dateCreated',
                'sent_at' => 'dateSent',
                'updated_at' => 'dateUpdated',
                'balance_after_changes' => 'balanceAfterChanges',
                'comment' => 'comment',
                'comment_admin' => 'commentPayment',
                'ext_pay_sys' => 'externalPaySystem',
                'amount_locked' => 'sum_locked',
                'refcode' => 'refcode',
                'useragent' => 'useragent',
                'ip' => 'ip',
                'host' => 'domain',
                'balance_type_name' => 'balanceType',
                'from_wallet_id' => 'fromBalanceId',
            ],
        ],
    ],
    'users-stats-bonuses' => [
        'class' => \app\back\modules\task\actions\UsersStatsBonusesSmenTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-balance-transactions',
            'aliases' => [
                'transaction_id' => 'id',
                'user_id' => 'user_id',
                'to_wallet_id' => 'balance_id',
                'amount_orig' => 'sum',
                'currency' => 'currency',
                'created_at' => 'created_at',
                'updated_at' => 'created_at',
                'source_name' => 'source_name',
                'remote_id' => 'source_id',
                'comment_admin' => 'admin_comment',
                'transaction_type' => 'transaction_type',
                'metadata' => 'metadata',
            ],
        ],
    ],
    'users-stats-fun' => [
        'class' => \app\back\modules\task\actions\UsersStatsFunSmenTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/fun-balance-transactions',
            'aliases' => [
                'transaction_id' => 'id',
                'user_id' => 'user_id',
                'amount_orig' => 'sum',
                'currency' => 'currency',
                'created_at' => 'created_at',
                'updated_at' => 'created_at',
                'source_name' => 'source_name',
            ],
        ],
    ],
    'users-logins' => [
        'class' => \app\back\modules\task\actions\UsersLoginsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/logins',
            'aliases' => [
                'login_id' => 'id',
                'user_id' => 'userId',
                'date' => 'date',
                'ip' => 'ip',
                'success' => 'success',
                'fail_reason' => 'failReason',
                'useragent' => 'userAgent',
                'code' => 'refCode',
                'aff_data' => 'affData',
                'host' => 'domain',
                'bulk_id' => 'bulkId',
                'smen_method_name' => 'loginMethod',
            ],
        ],
    ],
    'users-logins-rokeente-sessions' => [
        'class' => \app\back\modules\task\actions\UsersLoginsRokeenteSessionsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/user-rokeente',
            'aliases' => [
                'login_id' => 'loginId',
                'user_id' => 'userId',
                'session_id' => 'sessionKey',
                'created_at' => 'date',
            ],
        ],
    ],
    'users-wallets-real' => [
        'class' => \app\back\modules\task\actions\UsersWalletsRealTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/real-balances',
            'aliases' => [
                'user_id' => 'player_id',
                'wallet_id' => 'balance_id',
                'balance' => 'amount',
                'currency' => 'currency',
                'updated_at' => 'updated_at',
                'wagering_current_amount' => 'wagering_current_amount',
                'wagering_target_amount' => 'wagering_target_amount',
            ],
        ],
    ],
    'users-wallets-bonus' => [
        'class' => \app\back\modules\task\actions\UsersWalletsBonusTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-balances',
            'aliases' => [
                'user_id' => 'player_id',
                'wallet_id' => 'balance_id',
                'active' => 'active',
                'balance' => 'amount',
                'currency' => 'currency',
                'updated_at' => 'updated_at',
                'expired_at' => 'expired_at',
                'created_at' => 'created_at',
                'reset_at' => 'reset_at',
                'last_activate_at' => 'last_activate_at',
                'wager' => 'wager',
                'initial_amount' => 'initial_sum',
                'wagering_current_amount' => 'wagering_current_amount',
                'wagering_target_amount' => 'wagering_target_amount',
                'wagering_max_bet_amount' => 'wagering_max_bet_amount',
                'wagering_max_transfer_amount' => 'wagering_max_transfer_amount',
                'wagering_current_transfer_amount' => 'wagering_current_transfer_amount',
            ],
        ],
    ],
    'users-wallets-betting' => [
        'class' => \app\back\modules\task\actions\UsersWalletsBettingTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bets-balances',
            'aliases' => [
                'user_id' => 'user_id',
                'wallet_id' => 'id',
                'status' => 'status',
                'balance' => 'balance',
                'currency' => 'currency',
                'updated_at' => 'updated_at',
                'expired_at' => 'expired_at',
                'wagering_current_amount' => 'bet_sum',
                'wagering_target_amount' => 'wager_sum',
            ],
        ],
    ],
    'users-remarketing' => [
        'class' => \app\back\modules\task\actions\UsersRemarketingTask::class,
        'aliases' => [
            // Alias -> Site UD
            'gmslots.com' => Site::GMS,
            'maxbetslots.com' => Site::MS,
            'slotozal.com' => Site::SZL,
            'icecasino.com' => Site::ICG,
            'club-vulkan.com' => Site::CV,
            'https://klub-vulkan.net/' => Site::CV,
            'greencasino.com' => Site::VERDE, // Temporary after rename
            'verdecasino.com' => Site::VERDE,
            'gmsdeluxe.com' => Site::GMSD,
            'cvo1.xyz' => Site::RUBIN,
            'admiral777.com' => Site::ARM,
            'admiral777' => Site::ARM,
            'api.admiral777.com' => Site::ARM,
            'pharaonbet.com' => Site::PHB,
            'api.pharaonbet.com' => Site::PHB,
            'vulkanstars.com' => Site::VS,
            'api.vulkanstars.com' => Site::VS,
            'eldorado24.com' => Site::EL,
            'vulkan24' => Site::V24,
            'vulkan24.com' => Site::V24,
            'vulkan24club.com' => Site::V24,
            'https://vulkan24club.com' => Site::V24,
            'joykasino.net' => Site::JC,
            'joycasino.net' => Site::JC,
            'vdeluxe.com' => Site::ONX,
        ],
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/remarketing',
            'aliases' => [
                'user_id' => 'userId',
                'from_user_id' => 'externalUserId',
                'date' => 'date',
                'from_host' => 'host',
            ],
        ],
    ],
    'users-loyalty' => [
        'class' => \app\back\modules\task\actions\UsersLoyaltyTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/loyalty',
            'aliases' => [
                'user_id' => 'userId',
                'status_id' => 'status',
                'next_status_id' => 'nextStatus',
                'current_points' => 'currentPoints',
                'total_points' => 'totalPoints',
                'virtual_status_id' => 'virtualStatus',
                'status_points' => 'statusPoints',
                'status_points_to_finish' => 'statusPointsToFinish',
            ],
        ],
    ],
    'loyalty-statuses' => [
        'class' => \app\back\modules\task\actions\LoyaltyStatusesTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/loyalty-statuses',
            'aliases' => [
                'id' => 'id',
                'title' => 'title',
                'points_to_finish' => 'statusPointsToFinish',
            ],
        ],
    ],
    'users-loyalty-deposit' => [
        'class' => \app\back\modules\task\actions\UsersLoyaltyDepositTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/loyalty-user-deposit-status',
            'aliases' => [
                'user_id' => 'userId',
                'status_id' => 'status',
                'sum' => 'depositSum',
                'sum_to_next_status' => 'depositSumToNextStatus',
            ],
        ],
    ],
    'loyalty-deposit-statuses' => [
        'class' => \app\back\modules\task\actions\LoyaltyDepositStatusesTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/loyalty-deposit-statuses',
            'aliases' => [
                'id' => 'id',
                'title' => 'title',
            ],
        ],
    ],
    'users-wheel-fortune-prize' => [
        'class' => \app\back\modules\task\actions\UsersWheelFortunePrizeTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/loyalty-wheel-fortune-prize',
            'aliases' => [
                'prize_id' => 'id',
                'user_id' => 'userId',
                'created_at' => 'date',
                'prize' => 'prize',
                'spin_type' => 'spinType',
                'denomination' => 'denomination',
                'platform' => 'platform',
                'currency' => 'currency',
                'sum' => 'sum',
                'invoice' => 'invoice',
                'price' => 'price',
            ],
        ],
    ],
    'users-wheel-fortune-statistics' => [
        'class' => \app\back\modules\task\actions\UsersWheelFortuneStatisticsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/wheel-fortune-spin-statistics',
            'aliases' => [
                'stat_id' => 'id',
                'user_id' => 'userId',
                'spin_type' => 'spinType',
                'pay_sum' => 'paySum',
                'currency' => 'currency',
                'level_number' => 'levelNumber',
                'created_at' => 'createdAt',
                'denomination' => 'denomination',
                'virtual_status' => 'virtualStatus',
            ],
        ],
    ],
    'users-rebills' => [
        'class' => \app\back\modules\task\actions\UsersRebillsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/rebills',
            'aliases' => [
                'rebill_id' => 'id',
                'user_id' => 'userId',
                'finished_at' => 'finishedAt',
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
                'is_deleted' => 'isDeleted',
            ],
        ],
    ],
    'bonus-event-option' => [
        'class' => \app\back\modules\task\actions\BonusEventOptionTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonus-event-option',
            'aliases' => [
                'bonus_id' => 'bonusId',
                'option_key' => 'optionKey',
                'value' => 'value'
            ],
        ],
    ],
    'bonus' => [
        'class' => \app\back\modules\task\actions\BonusTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonus-list',
            'aliases' => [
                'bonus_id' => 'id',
                'event' => 'eventAlias',
                'started_at' => 'startedAt',
                'ended_at' => 'endedAt',
                'name' => 'name',
                'enabled' => 'enabled',
                'type' => 'typeAlias',
                'list_name' => 'listName',
                'ref_code' => 'refCode',
                'login_ref_code' => 'loginRefCode',
            ],
        ],
    ],
    'bonus-activated-user' => [
        'class' => \app\back\modules\task\actions\BonusActivatedUserTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-activated-user',
            'aliases' => [
                'bonus_activated_id' => 'id',
                'user_id' => 'userId',
                'bonus_id' => 'bonusId',
                'started_at' => 'startedAt',
                'ended_at' => 'endedAt',
                //'worked' => 'worked',
            ],
        ],
    ],
    'bonus-user-progress' => [
        'class' => \app\back\modules\task\actions\BonusUserProgressTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-user-progress',
            'aliases' => [
                'bonus_user_progress_id' => 'id',
                'user_id' => 'userId',
                'operator_id' => 'operator',
                'date' => 'date',
                'sum' => 'sum',
                'free_bet_sum' => 'freeBetSum',
                'bonus_id' => 'bonusId',
                'bonus_prize' => 'bonusPrize',
            ],
        ],
    ],
    'bonus-user-progress-free-spins' => [
        'class' => \app\back\modules\task\actions\BonusUserProgressFreeSpinsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/free-spins',
            'aliases' => [
                'free_spins_id' => 'id',
                'bonus_user_progress_id' => 'bonusUserProgressId',
                'user_id' => 'userId',
                'count' => 'count',
                'type' => 'type',
                'win_sum' => 'winSum',
                'wager' => 'wager',
                'updated_at' => 'lastUsed',
                'bonus_id' => 'bonusId',
                'game_id' => 'gameId',
                'options' => 'options',
                'started_at' => 'startedAt',
                'enabled' => 'enabled',
                'portional' => 'portional',
            ],
        ],
    ],
    'bonus-users-activity' => [
        'class' => \app\back\modules\task\actions\BonusUsersActivityTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-user-activity',
            'aliases' => [
                'activity_id' => 'id',
                'user_id' => 'user_id',
                'bonus_id' => 'bonus_id',
                'status' => 'status',
                'created_at' => 'created_at',
            ],
        ],
    ],
    'bonus-user-deposit' => [
        'class' => \app\back\modules\task\actions\BonusUserDepositTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-user-deposit',
            'aliases' => [
                'bonus_deposit_id' => 'id',
                'user_id' => 'userId',
                'bonus_id' => 'bonusId',
                'transaction_id' => 'paymentId',
                'bonus_user_progress_id' => 'bonusProgressId',
                'created_at' => 'madeAt',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'bonus-black-list' => [
        'class' => \app\back\modules\task\actions\BonusBlackListTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-users-black-list',
            'aliases' => [
                'user_id' => 'userId',
                'enabled' => 'enabled',
                'auto' => 'auto',
                'created_at' => 'createdAt',
            ],
        ],
    ],
    'bonuses-for-points-product' => [
        'class' => \app\back\modules\task\actions\BonusesForPointsProductTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonuses-for-points-product',
            'aliases' => [
                'id' => 'id',
                'name' => 'name',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'bonuses-for-points-product-bonus' => [
        'class' => \app\back\modules\task\actions\BonusesForPointsProductBonusTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonuses-for-points-product-bonus',
            'aliases' => [
                'product_id' => 'productId',
                'bonus_id' => 'bonusId',
            ],
        ],
    ],
    'bonuses-for-points-product-sale' => [
        'class' => \app\back\modules\task\actions\BonusesForPointsProductSaleTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonuses-for-points-product-sale',
            'aliases' => [
                'id' => 'id',
                'user_id' => 'userId',
                'product_id' => 'productId',
                'sold_at' => 'saledAt',
                'product_price' => 'productPrice',
                'points_exchange_rate' => 'userPointsExchangeRate',
            ],
        ],
    ],
    'bonus-users-promo-codes' => [
        'class' => \app\back\modules\task\actions\BonusUsersPromoCodesTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bonus-user-promo-code-log',
            'aliases' => [
                'bonus_users_promo_code_id' => 'id',
                'user_id' => 'userId',
                'bonus_id' => 'bonusId',
                'promo_code' => 'promoCode',
                'created_at' => 'createdAt',
                'status' => 'status',
                'error' => 'error',
            ],
        ],
    ],
    'users-devices' => [
        'class' => \app\back\modules\task\actions\UsersDevicesTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users-devices',
            'aliases' => [
                'user_id' => 'userId',
                'value' => 'deviceId',
                //'device_os' => 'deviceOs',
                'created_at' => 'dateCreate',
                'updated_at' => 'dateLogin',
                'browser_key' => 'browserKey',
                'browser_token' => 'browserToken',
                'endpoint_url' => 'endpointUrl',
                'useragent' => 'userAgent',
                'app_name' => 'appName',
            ],
        ],
    ],
    'login-tokens-smen' => [
        'class' => \app\back\modules\task\actions\LoginTokensTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/login-tokens',
            'aliases' => [
                'user_id' => 'userId',
                'login_token' => 'loginToken',
            ],
        ],
    ],
    'tournaments' => [
        'class' => \app\back\modules\task\actions\TournamentsSmenTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/tournaments',
            'aliases' => [
                'tournament_id' => 'tournamentId',
                'title' => 'title',
                'started_at' => 'dateStart',
                'finished_at' => 'dateEnd',
                'status' => 'status',
            ],
        ],
    ],
    'tournaments-users' => [
        'class' => \app\back\modules\task\actions\TournamentsUsersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/tournament-participants',
            'aliases' => [
                'tournament_id' => 'tournamentId',
                'user_id' => 'userId',
                'scores' => 'scores',
                'medal_place' => 'medalPlace',
                'created_at' => 'dateCreated',
                'updated_at' => 'dateUpdated',
            ],
        ],
    ],
    'users-games-chosen' => [
        'class' => \app\back\modules\task\actions\UsersGamesChosenTask::class,
        'gamesSource' => \app\back\entities\GameSource::SOURCE_SMEN,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/games-favourite',
            'aliases' => [
                //'id' => 'id',
                'user_id' => 'userId',
                'game_code' => 'gameId',
                'is_deleted' => 'isDeleted',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'games-sources' => [
        'class' => \app\back\modules\task\actions\GamesSourcesTask::class,
        'gamesSource' => \app\back\entities\GameSource::SOURCE_SMEN,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/games',
            'aliases' => [
                'enabled' => 'enabled',
                'game_name' => 'name',
                'game_vendor' => 'providerDeveloperName',
                'game_code' => 'id',
                'game_platform' => 'platform',
                'game_vendor_old' => 'developerName',
            ],
        ],
    ],
    'users-games-tokens' => [
        'class' => \app\back\modules\task\actions\UsersGamesTokensTask::class,
        'gamesSource' => \app\back\entities\GameSource::SOURCE_SMEN,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/games-sessions',
            'curlTimeout' => 300,
            'aliases' => [
                'token_id' => '_id',
                'user_id' => 'userId',
                'balance_type' => 'balanceType',
                'session_type_is_free' => 'free',
                'game_code' => 'gameId',
                'created_at' => 'createdAt',
                'useragent' => 'clientUserAgent',
                'ip' => 'clientIp',
                'ref_code' => 'refCode',
                'host' => 'domain',
                'last_action_at' => 'lastLaunchAt',
                'bet_amount' => 'betAmount',
                'bet_count' => 'betCount',
                'win_amount' => 'winAmount',
                'win_count' => 'winCount',
                'currency' => 'currency',
                'win_max' => 'winMax',
                'provider_integration_name' => 'providerIntegrationName',
                'launchData' => 'launchData',
            ],
        ],
    ],
    'users-games-bets' => [
        'class' => \app\back\modules\task\actions\import\UsersGamesBetsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/bets',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'bet_id'  => 'id',
                'created_at' => 'createdAt',
                'last_action_at' => 'updatedAt',
                'status_name' => 'status',
                //'type_name' => 'type',
                'settle_type_name' => 'settleType',
                'user_id' => 'userId',
                'currency' => 'currency',
                'bet_amount' => 'sum',
                'win_amount' => 'settleSum',
                'free_bet_id' => 'freeBetId',
                // 'balanceId' => 'balanceId',
                // 'odds' => 'metadata',
            ],
        ],
    ],
    'users-events' => [
        'class' => \app\back\modules\task\actions\UsersEventsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/user-events',
            'aliases' => [
                'remote_id' => 'id',
                'user_id' => 'userId',
                'type' => 'name',
                'source' => 'source',
                'ip' => 'ip',
                'useragent' => 'userAgent',
                'host' => 'domain',
                'created_at' => 'createdAt',
                'info' => 'attributes',
            ],
        ],
    ],
    'lotteries' => [
        'class' => \app\back\modules\task\actions\LotteriesTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/lotteries',
            'aliases' => [
                'lottery_id' => 'id',
                'name' => 'name',
                'start_at' => 'startAt',
                'finish_at' => 'finishAt',
                'prizes_distributed' => 'prizesDistributed',
                'is_material' => 'isMaterial',
                'prize_fund' => 'prizeFund',
                'redemption_type' => 'redemptionType',
                'is_deleted' => 'isDeleted',
                'tickets_distribution_reason_id' => 'ticketFor',
            ],
        ],
    ],
    'lottery-tickets' => [
        'class' => \app\back\modules\task\actions\LotteryTicketsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/lottery-tickets',
            'aliases' => [
                'ticket_id' => 'id',
                'lottery_id' => 'lotteryId',
                'user_id' => 'userId',
                'is_gold' => 'isGold',
                'is_disabled' => 'isDisabled',
                'created_at' => 'createdAt',
            ],
        ],
    ],
    'lottery-wins' => [
        'class' => \app\back\modules\task\actions\LotteryWinsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/lottery-winners',
            'aliases' => [
                'win_id' => 'id',
                'lottery_id' => 'lotteryId',
                'ticket_id' => 'ticketId',
                'prize_id' => 'prizeId',
                'is_distributed' => 'isDistributed',
                'user_name' => 'userName',
                'expired_at' => 'expiredAt',
                'updated_at' => 'updatedAt',
                'place' => 'place',
                'sum' => 'sum',
                'prize' => 'prize',
                'is_real' => 'isReal',
                'real_user_id' => 'prizeRealUserId',
            ],
        ],
    ],
    'sync-secret-mirrors' => [
        'class' => \app\back\modules\task\actions\sync\SecretMirrorsTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenSecretMirrorsRequest::class,
            'modeUrls' => [
                \app\back\modules\task\requests\SmenSecretMirrorsRequest::MODE_CREATE_MIRROR => 'api/analytics/mirror/create',
                \app\back\modules\task\requests\SmenSecretMirrorsRequest::MODE_REMOVE_MIRROR => 'api/analytics/mirror/remove',
                \app\back\modules\task\requests\SmenSecretMirrorsRequest::MODE_ADD_USER => 'api/analytics/mirror/add-user',
                \app\back\modules\task\requests\SmenSecretMirrorsRequest::MODE_REMOVE_USER => 'api/analytics/mirror/remove-user',
            ],
            'aliases' => [
                'success' => 'success',
                'entity' => 'entity',
                'message' => 'message',
            ],
        ],
    ],
    'secret-mirrors' => [
        'class' => \app\back\modules\task\actions\SecretMirrorsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/secret-mirror',
            'aliases' => [
                'url' => 'url',
                'created_at' => 'createdAt',
            ],
        ],
    ],
    'secret-mirrors-users' => [
        'class' => \app\back\modules\task\actions\SecretMirrorsUsersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/secret-mirror-user',
            'aliases' => [
                'user_id' => 'userId',
                'url' => 'url',
                'created_at' => 'createdAt',
            ],
        ],
    ],
    'sync-bets-white-list-users' => [
        'class' => \app\back\modules\task\actions\sync\BetsWhiteListUsersTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/betting/add-user',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'success' => 'success',
                'message' => 'message',
            ],
        ],
    ],
    'users-unsubscribe' => [
        'class' => \app\back\modules\task\actions\UsersUnsubscribeTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users-unsubscribe',
            'aliases' => [
                'user_id' => 'userId',
                'email_opt_out' => 'optOut',
                'updated_at' => 'updatedAt'
            ],
        ],
    ],
    'users-blocks' => [
        'class' => \app\back\modules\task\actions\UsersBlocksTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users-block-reasons',
            'aliases' => [
                'user_id' => 'userId',
                'updated_by_user_id' => 'operatorId',
                'comment' => 'reason',
                'updated_at' => 'createdAt',
                'source' => 'initBy',
                'operation' => 'operation',
            ],
        ],
    ],
    'users-self-exclude' => [
        'class' => \app\back\modules\task\actions\UsersSelfExcludeTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users-self-exclusions',
            'aliases' => [
                'user_id' => 'userId',
                'self_excluded' => 'isBlocked',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'users-multi-accounts' => [
        'class' => \app\back\modules\task\actions\UsersMultiAccountsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/multi-accounts',
            'aliases' => [
                'user_id' => 'userId',
                'parent_user_id' => 'parentUserId',
                'reason' => 'reason',
                'created_at' => 'created',
            ],
        ],
    ],
    'users-password-reset' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/user/passwords/reset',
        ]
    ],
    'users-block' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/users/disable',
        ],
    ],
    'users-unblock' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/users/enable',
        ],
    ],
    'bonus-wallet-reset' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenBonusWalletResetRequest::class,
            'url' => 'api/analytics/wallets/:userId/bonus_balance/:balanceId/reset',
            'isPost' => true,
        ],
    ],
    'users-add-to-bonus-black-list' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/user/bonus/add-to-black-list',
        ],
    ],
    'users-remove-from-bonus-black-list' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/user/bonus/remove-from-black-list',
        ],
    ],
    'users-add-to-ignore-list' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/users/developer-status',
        ]
    ],
    'users-bonuses-activation' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonuses/page',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'bonus_id' => 'id',
                'bonus_name' => 'systemName',
                'is_activated' => 'isActivated',
                'started_at' => 'startedAt',
                'ended_at' => 'endedAt',
                'type' => 'type',
                'promocodes' => 'promocodes',
                'prizes' => 'prizes',
                'min_deposit' => 'minDeposit',
//                'bonus_real_name' => 'name',
//                'must_activated' => 'mustActivated',
//                'hosts' => 'hosts',
//                'platform' => 'platform',
            ],
        ],
    ],
    'bonus-activate' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenBonusActivationRequest::class,
            'url' => 'api/analytics/bonuses/page/activate/:bonusId',
            'objectAsSingleRow' => true,
            'isPost' => true,
            'aliases' => [
                'success' => 'success',
            ],
        ],
    ],
    'split-tests' => [
        'class' => \app\back\modules\task\actions\SplitTestsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/split_info',
            'aliases' => [
                'slug' => 'slug',
                'status' => 'status',
                'title' => 'title',
                'begins_at' => 'begins_at',
                'ends_at' => 'ends_at',
                'user_limit' => 'user_limit',
                'limit_event_slug' => 'limit_event_slug',
                'updated_at' => 'updated_at',
            ],
        ],
    ],
    'split-tests-logs' => [
        'class' => \app\back\modules\task\actions\SplitTestsLogsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/split_log',
            'aliases' => [
                'log_id' => 'id',
                'identity_key' => 'identity_key',
                'split_slug' => 'split_slug',
                'split_group_slug' => 'split_group_slug',
                'event_slug' => 'event_slug',
                'created_at' => 'created_at',
                'extra_data' => 'extra_data',
            ],
        ],
    ],
    'split-tests-users' => [
        'class' => \app\back\modules\task\actions\SplitTestsUsersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/split_user_identity',
            'aliases' => [
                'user_id' => 'user_id',
                'identity_key' => 'identity_key',
                'refcode' => 'ref_code',
                'updated_at' => 'updated_at',
            ],
        ],
    ],
    'split-tests-users-skip-null-users' => [
        'class' => \app\back\modules\task\actions\SplitTestsUsersTask::class,
        'skipNullUsers' => true,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/split_user_identity',
            'aliases' => [
                'user_id' => 'user_id',
                'identity_key' => 'identity_key',
                'refcode' => 'ref_code',
                'updated_at' => 'updated_at',
            ],
        ],
    ],
    'ignore-users' => [
        'class' => \app\back\modules\task\actions\IgnoreUsersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/users-developer-status-reasons',
            'aliases' => [
                'user_id' => 'userId',
                'comment' => 'reason',
                'created_at' => 'createdAt',
                'is_ignore' => 'operation',
            ],
        ],
    ],
    'phone-confirmation-requests' => [
        'class' => \app\back\modules\task\actions\PhoneConfirmationRequestTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/verify',
            'aliases' => [
                'request_id' => 'id',
                'user_id' => 'userId',
                'created_at' => 'date',
                'updated_at' => 'updateDate',
                'smsc_id' => 'smscId',
                'phone_number' => 'phoneNumber',
                'is_success' => 'success',
                'type' => 'transactionType',
                'is_confirmed' => 'confirmed',
                'reason' => 'reason',
                'country' => 'country',
                'device_type' => 'deviceType',
            ],
        ],
    ],
    'sync-withdrawals' => [
        'class' => \app\back\modules\task\actions\sync\WithdrawalsTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenWithdrawalRequest::class,
            'curlTimeout' => 10,
        ],
    ],
    'get-user-spins' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenSpinsRequest::class,
            'url' => 'api/analytics/game-activity-log',
            'aliases' => [
                'userId' => 'userId',
                'game' => 'game',
                'action' => 'action',
                'launchAtMicro' => 'launchAtMicro',
                'balance' => 'balance',
                'amount' => 'amount',
                'roundType' => 'roundType',
                'balanceType' => 'balanceType',
                'actionId' => 'actionId',
                'symbols' => 'symbols',
                'realBalance' => 'realBalance',
                'bonusBalance' => 'bonusBalance',
                'bonusRefundSum' => 'bonusRefundSum',
                'bonusBetSum' => 'bonusBetSum',
                'session_token' => 'token',
            ],
        ],
    ],
    'get-user-cashback' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/user-cash-back',
            'aliases' => [
                'userId' => 'userId',
                'cashback' => 'cashback',
            ],
        ],
    ],
    'get-user-stash' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/stash-user-balance',
            'aliases' => [
                'userId' => 'userId',
                'amount' => 'amount',
                'currency' => 'currency',
            ],
        ],
    ],
    'add-payment-manual' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenPaymentCreateRequest::class,
            'url' => 'api/payment/create/:type/:userId/:sum/:currency/:uuid',
            'isPost' => true,
            'objectAsSingleRow' => true,
            'aliases' => [
                'success' => 'success',
            ],
        ],
    ],
    'add-bonus-manual' => [
        'class' => \app\back\modules\task\actions\SmenFetchAndCheckSuccessTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonuses/submit-bonus-form',
        ],
    ],
    'get-bonus-list' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/bonuses/get-prizes-form-settings',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'prize' => 'prize',
                'freeSpinsOptions' => 'freeSpinsOptions',
            ],
        ],
    ],
    'sites-mirrors' => [
        'class' => \app\back\modules\task\actions\SitesMirrorsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/site-mirrors',
            'aliases' => [
                //'external_id' => 'id',
                'host' => 'domain',
                //'external_created_at' => 'createdAt',
                'external_updated_at' => 'updatedAt',
                'is_seo' => 'isSeoDomain',
                'is_app' => 'isApplicationDomain',
                'for_toxic_user' => 'forToxicUser',
                'brand_name' => 'brand_name',
                'original_brand_id' => 'brandId',
            ],
        ],
    ],
    'users-documents' => [
        'class' => \app\back\modules\task\actions\UsersDocumentsSmenTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/documents/list',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'external_id' => 'id',
                'type' => 'type',
                'user_id' => 'userId',
                'download_url' => 'url',
                'external_created_at' => 'createdAt',
                'external_updated_at' => 'updatedAt',
                'aiStatus' => 'status',
                'aiValidationMessages' => 'validationMessages',
                'aiAdminMessage' => 'adminMessage',
                'aiScheduledAt' => 'aiScheduledAt',
                'aiCompletedAt' => 'aiCompletedAt',
                //'init_by_user' => 'initByUser',
                //'logged_in' => 'loggedIn',
            ],
        ],
    ],
    'bonus-offers' => [
        'class' => \app\back\modules\task\actions\BonusOffersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/analytics-personal-bonus',
            'aliases' => [
                'remote_id' => 'id',
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
                'expired_at' => 'expiredAt',
                'activated_at' => 'activatedAt',
                'user_id' => 'userId',
                'status_name' => 'newStatus',
                'operator_email' => 'operatorEmail',
                'deposit_sum_min' => 'minDepSum',
                'prize_sum_max' => 'maxPrize',
                'activation_time' => 'activationTime',
                'available_time' => 'availableTime',
                'prize_percent' => 'prizePercent',
                'wager' => 'wager',
                'prize_sum' => 'prizeSum',
                'condition_met_at' => 'prizeReceivedAt',
            ],
        ],
    ],
    'bonus-offer-create' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/analytics_personal_bonus/create',
            'objectAsSingleRow' => true,
            'isPost' => true,
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'remote_id' => 'id',
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'bonus-offer-update' => [
        'class' => \app\back\modules\task\FetchTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenBonusOfferUpdateRequest::class,
            'url' => 'api/analytics/analytics_personal_bonus/:bonusOfferId/:action',
            'objectAsSingleRow' => true,
            'isPost' => true,
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'remote_id' => 'id',
                'status_name' => 'status',
            ],
        ],
    ],
    'lootboxes' => [
        'class' => \app\back\modules\task\actions\LootboxesTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/lootbox-list',
            'aliases' => [
                'lootbox_id' => 'id',
                'title' => 'title',
                'enabled' => 'enabled',
            ],
        ],
    ],
    'lootbox-prizes' => [
        'class' => \app\back\modules\task\actions\LootboxPrizesTask::class,
        'request' => [
            'class' => SmenRequest::class,
            'url' => 'api/analytics/lootbox-prize-list',
            'aliases' => [
                'prize_id' => 'id',
                'title' => 'title',
                'type' => 'type',
                'prize_text' => 'prizeText',
            ],
        ],
    ],
    'lootbox-user-prize-log' => [
        'class' => \app\back\modules\task\actions\LootboxUserPrizeLogTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/lootbox-payments-log',
            'aliases' => [
                'log_id' => 'id',
                'user_id' => 'userId',
                'lootbox_id' => 'lootboxId',
                'amount' => 'sum',
                'currency' => 'currency',
                'platform' => 'platform',
                'multiplier' => 'multiplier',
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
                'transaction_id' => 'paymentId',
                'prizes' => 'prizes',
            ],
        ],
    ],
    'referral-referrers' => [
        'class' => \app\back\modules\task\actions\ReferralReferrersTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/referral_referrers',
            'aliases' => [
                'user_id' => 'userId',
                'created_at' => 'createdAt',
                'available_from' => 'availableFrom',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'referral-references' => [
        'class' => \app\back\modules\task\actions\ReferralReferencesTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/referral_references',
            'aliases' => [
                'user_id' => 'userId',
                'completed_at' => 'completedAt',
                'is_multi_account' => 'isMultiAccount',
                'created_at' => 'createdAt',
                'child_user_id' => 'referralUserId',
                'parent_user_id' => 'referrerUserId',
                'updated_at' => 'updatedAt',
            ],
        ],
    ],
    'referral-blacklist' => [
        'class' => \app\back\modules\task\actions\ReferralBlacklistsTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/referral_blacklist',
            'aliases' => [
                'user_id' => 'userId',
                'block_reason' => 'blockReason',
                'created_at' => 'createdAt',
                'deleted_at' => 'deletedAt',
            ],
        ],
    ],
    'users-tickets' => [
        'class' => \app\back\modules\task\actions\import\UsersTicketsSmenTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => '/api/analytics/task_tracker/task_list',
            'parserConfig' => JsonParser::class,
            'aliases' => [
                'product_ticket_id' => 'productId',
                'type' => 'type',
                'user_id' => 'userId',
                'status' => 'status',
                'invoice_id' => 'details.paymentNumber',
                'amount_from_user' => 'details.userPaymentAmount',
                'source' => 'source', // source of created_at - @elden
                'created_at' => 'createdAt',
                'updated_at' => 'updatedAt',
                'files' => 'files',
                // 'analyticsId',
                // 'jiraId',
                // 'userAgent'
                // 'userIp'
            ],
        ],
    ],
    'users-tickets-files' => [
        'class' => \app\back\modules\task\actions\import\UsersTicketsFilesSmenTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\SmenTicketsFileRequest::class,
            'url' => 'api/analytics/task_tracker/task/:taskId/file/:fileName',
        ],
    ],

    'users-events-checkout' => [
        'class' => \app\back\modules\task\actions\UsersEventsCheckoutTask::class,
        'request' => [
            'class' => SmenFromToRequest::class,
            'url' => 'api/analytics/cashier-front-events',
            'aliases' => [
                'event_type_name' => 'event',
                'user_id' => 'user_id',
                'context_id' => 'payment_context',
                'client_created_at' => 'user_event_time',
                'server_received_at' => 'server_event_time',
                'useragent' => 'user_agent',
                'ip' => 'ip',
                'product_open_id' => 'open_id',
            ],
        ],
    ],

    // Update tasks
    'update-metrics-from-bonus-user-progress' => \app\back\modules\task\actions\update\MetricsFromBonusUserProgressTask::class,
    'update-split-tests-logs-users' => \app\back\modules\task\actions\update\SplitTestsLogsUsersTask::class,
    'update-users-max-win' => \app\back\modules\task\actions\update\UsersMaxWinTask::class,
    'update-users-rebills-clean' => \app\back\modules\task\actions\update\UsersRebillsCleanTask::class,
    'update-withdrawals-delay' => \app\back\modules\task\actions\update\WithdrawalsDelayTask::class,
    'update-withdrawals-planned' => \app\back\modules\task\actions\update\WithdrawalsPlannedTask::class,

    // Sync tasks
    'sync-bonus-black-list-add-users' => \app\back\modules\task\actions\sync\BonusBlackListAddUsersTask::class,
    'sync-bonus-black-list-add-cid-users' => \app\back\modules\task\actions\sync\BonusBlackListAddCidUsersTask::class,
    'sync-bonus-black-list-remove-users' => \app\back\modules\task\actions\sync\BonusBlackListRemoveUsersTask::class,
];

return [
    Res::PLATFORM_SMEN => $tasks,
    Res::CV => [
        'users-stats' => [
            'splitPeriod' => 'PT1H',
        ],
    ],
];
