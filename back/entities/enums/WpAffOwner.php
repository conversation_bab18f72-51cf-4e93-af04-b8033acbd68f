<?php

declare(strict_types=1);

namespace app\back\entities\enums;

use app\back\entities\Refcode;
use app\back\repositories\WpAffOwnerGroups;

class WpAffOwner extends BaseGroupedEnum
{
    public const int GROUP_GG = 1;
    public const int GROUP_STP = 2;
    public const int GROUP_SEO = 3;
    public const int GROUP_VB = 4;
    public const int GROUP_VP = 5;
    public const int GROUP_WP = 6;
    public const int GROUP_YOUTUBE = 7;
    public const int GROUP_HUFFSON = 8;
    public const int GROUP_VIPAFF = 9;

    public const array GROUPS = [
        self::GROUP_GG => 'GG',
        self::GROUP_STP => 'STP',
        self::GROUP_SEO => 'SEO',
        self::GROUP_VB => 'VB',
        self::GROUP_VP => 'VP',
        self::GROUP_WP => 'WP',
        self::GROUP_YOUTUBE => 'Youtube',
        self::GROUP_HUFFSON => 'Huffson',
        self::GROUP_VIPAFF => 'VipAff',
    ];

    public const string GG_CASINO_BETTING = 'GG Casino-Betting';

    public const string VIPAFF_FAKE_OWNER = 'VipAffFakeOwner';

    protected static array $map = [];

    protected static function name(): string
    {
        return 'wp_aff_owner';
    }

    public static function groups(): array
    {
        return static::GROUPS;
    }

    protected static function groupsTable(): string
    {
        return WpAffOwnerGroups::TABLE_NAME;
    }

    public static function vipAffFakeOwnerExpression(string $webmasterAffOwner, ?string $refcodeTableAlias = null): string
    {
        if (!isset($refcodeTableAlias)) {
            return $webmasterAffOwner;
        }

        $prefixes = implode('|', array_map('preg_quote', Refcode::VIP_AFF_PREFIXES));

        return "CASE WHEN ($refcodeTableAlias.code ~* '^($prefixes).*') THEN '" . self::VIPAFF_FAKE_OWNER . "' ELSE $webmasterAffOwner END";
    }
}
