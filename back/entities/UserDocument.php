<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\PgArray;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\FloatValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonObjectValidator;
use app\back\components\validators\PgArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UuidValidator;

class UserDocument extends BaseEntity
{
    public const int STATUS_UNVERIFIED = 1;
    public const int STATUS_DECLINED = 2;
    public const int STATUS_VALID = 3;

    public const array STATUSES = [
        self::STATUS_UNVERIFIED => 'Unverified',
        self::STATUS_DECLINED => 'Declined',
        self::STATUS_VALID => 'Valid',
    ];

    public const string AI_V_KEY_VALID = 'valid';
    public const string AI_V_KEY_MESSAGES = 'messages';

    public const int AI_V_DOC_NOT_FOUND = 1;
    public const int AI_V_LOW_DOC_PROBABILITY = 2;
    public const int AI_V_LOW_DOC_QUALITY = 3;
    public const int AI_V_LOW_FACE_QUALITY = 4;
    public const int AI_V_ERROR_RESPONSE = 5;
    public const int AI_V_SERVICE_INVALID_RESPONSE = 6;
    public const int AI_V_SERVICE_TASK_NOT_FOUND = 7;
    public const int AI_V_ERROR = 8;
    public const array AI_VALIDATIONS = [
        self::AI_V_DOC_NOT_FOUND => 'Document not found',
        self::AI_V_LOW_DOC_PROBABILITY => 'Document low probability',
        self::AI_V_LOW_DOC_QUALITY => 'Document low quality',
        self::AI_V_LOW_FACE_QUALITY => 'Document low faces quality',
        self::AI_V_ERROR => 'AI service error',
        self::AI_V_ERROR_RESPONSE => 'AI service error response',
        self::AI_V_SERVICE_INVALID_RESPONSE => 'AI service invalid response',
        self::AI_V_SERVICE_TASK_NOT_FOUND => 'AI service task not found',
    ];

    public const string TAG_ID_CARD = 'id_card';
    public const string TAG_PASSPORT = 'passport';
    public const string TAG_DIVING_LICENSE = 'driving_license';
    public const string TAG_BANK_CARD = 'back_card';
    public const string TAG_REQUISITE = 'requisite';
    public const string TAG_SELFIE = 'selfie';
    public const string TAG_UNKNOWN = 'unknown';
    public const string TAG_NOT_RECOGNIZED = 'not_recognized';

    public const array TAGS = [
        self::TAG_ID_CARD => 'Id card',
        self::TAG_PASSPORT => 'Passport',
        self::TAG_DIVING_LICENSE => 'Driving license',
        self::TAG_BANK_CARD => 'Bank card',
        self::TAG_REQUISITE => 'Requisite',
        self::TAG_SELFIE => 'Selfie',
    ];

    public int $id;
    #[IntValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[StringValidator(17, 17)]
    public string $filename;
    #[PgArrayValidator([StringValidator::class, 'min' => 1, 'max' => 32], false)]
    public PgArray $tags;
    #[BooleanValidator]
    public ?bool $face_processed;
    #[IntValidator]
    public ?int $height;
    #[IntValidator]
    public ?int $width;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[StringValidator(2, 2)]
    public ?string $country;
    #[IntValidator]
    public ?int $created_by;
    #[IntValidator]
    public ?int $updated_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[UuidValidator]
    public ?string $external_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $external_created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $external_updated_at;
    #[BooleanValidator]
    public ?bool $external_approve_required;
    #[BooleanValidator]
    public ?bool $force_face_recognize;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status = self::STATUS_UNVERIFIED;
    #[FloatValidator(0, 100)]
    public ?float $selfie_quality;
    #[UuidValidator]
    public ?string $etag; // md5 or other hash from minio
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $deleted_at;
    #[IntInArrayValidator(UserDocumentProgress::TYPES)]
    public ?int $doc_type;
    #[JsonObjectValidator(3)]
    public ?array $ai_validation;
    #[FloatValidator(0, 100)]
    public ?float $doc_quality;
    #[IntValidator]
    public int $page_num = 0;

    private array $changedFields = [];

    public static function composeStoragePath(int $siteId, int $userId, string $filename): string
    {
        return "{$siteId}/{$userId}/{$filename}";
    }

    public function storagePath(): string
    {
        if (empty($this->site_id) || empty($this->user_id) || empty($this->filename)) {
            throw new \RuntimeException('Data not enough to storage document path');
        }

        return "$this->site_id/$this->user_id/$this->filename";
    }

    public function pushChangedFields(string ...$field): void
    {
        foreach ($field as $f) {
            $this->changedFields[$f] = true;
        }
    }

    public function getChangedFields(): array
    {
        return array_keys($this->changedFields);
    }

    public function hasChangedFields(): bool
    {
        return !empty($this->changedFields);
    }

    public function isFieldChanged(string $field): bool
    {
        return isset($this->changedFields[$field]);
    }

    public function forgetChangedFields(): void
    {
        $this->changedFields = [];
    }
}
