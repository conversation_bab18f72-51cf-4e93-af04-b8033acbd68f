<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;

class BonusOffer extends BaseEntity
{
    public const int STATUS_ACTIVE = 1;
    public const int STATUS_MET = 2;
    public const int STATUS_EXPIRED = 3;
    public const int STATUS_CANCELLED = 4;
    public const int STATUS_AVAILABLE = 5;

    public const array STATUSES = [
        self::STATUS_ACTIVE => 'Active',
        self::STATUS_MET => 'Met',
        self::STATUS_EXPIRED => 'Expired',
        self::STATUS_CANCELLED => 'Cancelled',
        self::STATUS_AVAILABLE => 'Available',
    ];

    public const array ACTIVATION_TIMES = [
        1, 2, 3, 4, 5, 6, 12, 24, 48, 72, 120, 168
    ];

    public const array PRIZE_PERCENTS = [
        15, 20, 30, 40, 50, 75, 80, 100, 110, 115, 120, 125, 150
    ];

    public const int MIN_MAX_TRANSFER = 1;
    public const int MAX_MAX_TRANSFER = 20;

    public int $id;
    #[IntValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IntValidator]
    public int $operator_id; // TODO: rename to employee_id
    #[IntInArrayValidator(self::STATUSES)]
    public int $status;
    #[MoneyValidator]
    public string $deposit_sum_min;
    #[MoneyValidator]
    public ?string $prize_sum_max;
    #[IntInArrayValidator(self::ACTIVATION_TIMES, true)]
    public int $activation_time;
    #[IntInArrayValidator(self::PRIZE_PERCENTS, true)]
    public int $prize_percent;
    #[IntValidator(10)]
    public int $wager;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $updated_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $condition_met_at;
    #[MoneyValidator]
    public ?string $prize_sum;
    #[IntValidator]
    public ?int $bonus_log_id;
    #[IntValidator]
    public ?int $remote_id;
    #[IntValidator(self::MIN_MAX_TRANSFER, self::MAX_MAX_TRANSFER)]
    public ?int $max_transfer;
    #[MoneyValidator]
    public ?string $max_wagering_amount; // GI ONLY
    #[MoneyValidator]
    public ?string $max_transfer_amount; // GI ONLY
    #[IntValidator]
    public ?int $wager_hours; // SMEN ONLY
    #[IntInArrayValidator(self::ACTIVATION_TIMES, true)]
    public ?int $available_time;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $expired_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $activated_at;

    public function finishedAt(): \DateTimeImmutable
    {
        return $this->created_at->add(\DateInterval::createFromDateString($this->activation_time . 'hours'));
    }

    public static function getStatus(int $status): string
    {
        return self::STATUSES[$status];
    }

    public static function getPrizePercents(): array
    {
        $keys = self::PRIZE_PERCENTS;
        $values = array_map(static fn ($el) => $el . '%', $keys);

        return array_combine($keys, $values);
    }

    public static function getPrizePercent($percent)
    {
        return self::getPrizePercents()[$percent] ?? $percent;
    }

    public static function getActivationTimeVariants(): array
    {
        $keys = self::ACTIVATION_TIMES;
        $values = array_map(static fn ($el) => $el . 'h', $keys);

        return array_combine($keys, $values);
    }

    public static function getActivationTime(int $time): string
    {
        return self::getActivationTimeVariants()[$time];
    }
}
