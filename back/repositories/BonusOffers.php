<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\BonusOffer;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class BonusOffers extends BaseRepository
{
    public const string ENTITY_CLASS = BonusOffer::class;
    public const string TABLE_NAME = 'bonus_offers';
    public const array PRIMARY_KEY = ['id'];

    public static function userHasOffer(ConnectionInterface $db, int $siteId, int $userId): bool
    {
        return (new Query($db))
            ->select(['COUNT(*)'])
            ->from(['bo' => self::TABLE_NAME])
            ->where([
                'AND',
                ['bo.site_id' => $siteId],
                ['bo.user_id' => $userId],
                ['bo.status' => BonusOffer::STATUS_ACTIVE],
            ])
            ->scalar() > 0;
    }
}
