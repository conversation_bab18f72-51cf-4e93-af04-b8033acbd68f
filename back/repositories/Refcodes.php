<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\helpers\Db;
use app\back\entities\Refcode;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class Refcodes extends BaseRepository
{
    public const string ENTITY_CLASS = Refcode::class;
    public const string TABLE_NAME = 'refcodes';
    public const array PRIMARY_KEY = ['id'];

    private const int WEBMASTER_ID_MAX_LENGTH = 9;

    public static function appIdExpression($table = 'r', $field = 'code'): string
    {
        return "ARRAY_TO_STRING(REGEXP_MATCH($table.$field, '(?:\.|_)(\d{2,10})(?:-|_)(?:android|ios|outsource)-|_FB_[a-zA-Z]+_(\d{2,10})'), ',')";
    }

    public static function programExpression($table = 'r', $field = 'code'): string
    {
        return "SUBSTRING($table.$field FROM 'p(\\d{1,10})')::bigint";
    }

    public static function prefixAggExpression($table = 'r', $field = 'code'): string
    {
        return "(ARRAY_AGG(SUBSTRING($table.$field FROM '^\w+?(?=_)')))[1]";
    }

    public static function prefixAggFilterExpression(ConnectionInterface $db, array $filterPrefixes, $table = 'r', $field = 'code'): string
    {
        $refcodePrefix = implode(',', array_map(static fn($v) => $db->quoteValue($v), $filterPrefixes));
        return "ARRAY_AGG(SUBSTRING($table.$field FROM '^\w+?(?=_)')) && ARRAY[" . $refcodePrefix . "]";
    }

    /**
     * cs_BQBHFAAAbEwAAPRvAQA.2020-07.03.3snet_ru-sb_6889 => 3snet
     * cs_BADOBgAATh0AALgkAAA.hltv_topbanner.2018-11.11 => hltv
     */
    public static function publisherExp(string $table = 'r', string $field = 'code'): string
    {
        $regexp = '^cs_[\w-]+\.(?:\d{4}-\d{2}.\d{2}\.)?(?!\d{4}-\d{2})([^_.\n]+)(?:.*?\.\d{4}-\d{2})?';

        return "SUBSTRING($table.$field FROM '$regexp')";
    }

    public static function webmasterIdExp(): string
    {
        $wpPrefixes = self::wpPrefixesRegExp();
        $wpPrefixesYs = self::wpPrefixesRegExp(Refcode::YS_AFF_PREFIXES);

        return "^(?:(?:$wpPrefixes)_w|" . Refcode::SLOTTY_AFF_PREFIX . ")([0-9]{1," . self::WEBMASTER_ID_MAX_LENGTH . "})|^(?:(?:$wpPrefixesYs)_)([0-9a-f]{6}).*$";
    }

    public static function wpPrefixesRegExp(array $prefixes = Refcode::WP_PREFIXES): string
    {
        return implode('|', $prefixes);
    }

    public static function segmentCase($table = 'r', $field = 'code'): string
    {
        $segments = implode('|', array_map('preg_quote', Refcode::SEGMENTS));

        return "COALESCE((regexp_match({$table}.{$field}, '_({$segments})'))[1], '" . preg_quote(Refcode::DEFAULT_SEGMENT, '\\') . "')";
    }

    public static function crmChannelsCase($table = 'r', $field = 'code'): string
    {
        $prefix = Refcode::CRM_CHANNELS_PREFIX;

        $cases = [];
        foreach (Refcode::CRM_CHANNELS_POST_PREFIXES as $channel => $prefixes) {
            $prefixes = implode('|', array_map('preg_quote', $prefixes));
            $cases[] = "WHEN {$table}.{$field} ~ '^{$prefix}($prefixes)_' THEN $channel";
        }

        $cases = implode(' ', $cases);

        return "(CASE $cases END)";
    }

    public static function cleanCodeExpression(string $table = 'r', string $field = 'code'): string
    {
        $wpPrefixes = self::wpPrefixesRegExp();

        return "REGEXP_REPLACE($table.$field, '(($wpPrefixes)_w\\d+)[^_]*(p\\d+_.*)', '\\1\\3')";
    }

    public function resetTs(string $code): int
    {
        return $this->db->createCommand()
            ->update(self::TABLE_NAME, ['ts_id' => null], ['ILIKE', 'code', Db::escapeLikeVal($code) . '%', null])
            ->execute();
    }

    public function getIdByCode($code): int
    {
        static $list = [];

        if (!isset($list[$code])) {
            $id = $this->getIdByCodeClean($code);

            if ($id === null) {
                /** @var Refcode $entity */
                $entity = $this->validateAndCreate(['code' => $code]);
                $this->insert($entity);
                $id = $entity->id;
            }

            $list[$code] = $id;
        }

        return $list[$code];
    }

    private function getIdByCodeClean($code): ?int
    {
        return (new Query($this->db))
            ->select('id')
            ->from(self::TABLE_NAME)
            ->where(['code' => Refcode::clean($code)])
            ->limit(1)
            ->scalar() ?: null;
    }

    public function getCodeById(?int $id): ?string
    {
        static $list = [];

        if ($id === null) {
            return null;
        }

        if (!array_key_exists($id, $list)) {
            $code = (new Query($this->db))
                ->select(['code'])
                ->from(self::TABLE_NAME)
                ->where(['id' => $id])
                ->limit(1)
                ->scalar();

            if ($code === false) {
                $code = null;
            }

            $list[$id] = $code;
        }

        return $list[$id];
    }

    public static function castIntExp(string $alias = 'r'): string
    {
        $wpPrefixesYs = self::wpPrefixesRegExp(Refcode::YS_AFF_PREFIXES);

        return "CAST(CASE WHEN ($alias.webmaster_id ~* '^\d{1," . self::WEBMASTER_ID_MAX_LENGTH . "}$' AND $alias.code ~* '^(?!(?:$wpPrefixesYs)_).*$') THEN $alias.webmaster_id END as int)";
    }

    public static function wpJoinExpression(string $webmastersTableColumn, ?string $refcodesTableAlias = 'r', ?string $otherConditions = null): string
    {
        $expr = $webmastersTableColumn . ' = ' . self::castIntExp($refcodesTableAlias);

        if ($otherConditions) {
            $expr .= " AND $otherConditions";
        }

        return $expr;
    }
}
