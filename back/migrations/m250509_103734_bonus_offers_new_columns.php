<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250509_103734_bonus_offers_new_columns extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE bonus_offers ADD COLUMN available_time int");
        $this->sql("ALTER TABLE bonus_offers ADD COLUMN expired_at  timestamp(0)");
        $this->sql("ALTER TABLE bonus_offers ADD COLUMN activated_at  timestamp(0)");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE bonus_offers DROP COLUMN available_time");
        $this->sql("ALTER TABLE bonus_offers DROP COLUMN expired_at");
        $this->sql("ALTER TABLE bonus_offers DROP COLUMN activated_at");
    }
}
