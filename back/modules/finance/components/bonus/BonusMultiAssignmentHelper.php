<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus;

use app\back\components\Form;
use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\MoneyValidator;
use app\back\entities\User;
use app\back\entities\UserWallet;
use app\back\repositories\Users;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

trait BonusMultiAssignmentHelper
{
    protected const int MAX_LINES = 500;

    protected array $usersWithDetails = [];

    public function sessionMessagesResult(array $failedUsers): void
    {
        $allUsers = count($this->usersWithDetails);
        $failedUsersCount = count($failedUsers);
        $succeedUsers = $allUsers - $failedUsersCount;

        match ($failedUsersCount) {
            0 => $this->sessionMessages->success("Sent to $allUsers of $allUsers users"),
            $allUsers => $this->sessionMessages->error("Failed to sent to all users ($allUsers)"),
            default => $this->sessionMessages->info("Sent to $succeedUsers of $allUsers users. $failedUsersCount failed users are left in form.")
        };

        if ($failedUsersCount > 0) {
            $this->sessionMessages->error(implode("\n", $failedUsers));
        }
    }

    public function validateUsersMultiLine(int $siteId, string $multiline, bool $isAdvancedMode): ?string
    {
        $form = new class ($this->db, $siteId, $isAdvancedMode) {
            use Form;
            use BonusMultiAssignmentHelper;

            #[CallableValidator([self::class, 'userIdsOrUserIdsSumsValidator'], true)]
            public string $userId;

            public function __construct(
                public readonly ConnectionInterface $db,
                public readonly int $siteId,
                public readonly bool $isAdvancedMode
            ) {
            }
        };

        $errors = $form->validate(['userId' => $multiline]);
        return (count($errors)) ? explode(':', reset($errors))[1] : null;
    }

    public static function userIdsOrUserIdsSumsValidator(?string $value, self $form, array $context): ?string
    {
        if (isset($form->hasUserInForm) && $value === null) {
            return null;
        }

        $error = self::validateNumberOfColumnsInEveryLine($value, $context);
        if ($error !== null) {
            return $error;
        }

        $rows = self::buildUsersSumsArray($value);
        return self::validateCounts($rows)
            ?? self::validateColumns($rows, $form, $context)
            ?? self::validateUsersAndSetDetails($form, $rows);
    }

    private static function validateNumberOfColumnsInEveryLine(?string $value, array $context): ?string
    {
        foreach (Str::explodeText($value) as $index => $line) {
            $lineNumber = $index + 1;
            if (!preg_match('#^(?<userId>\d+)(?: (?<sum>\d+(\.\d\d)?))?$#', $line, $matches)) {
                return "Line $lineNumber format invalid";
            }

            if (!empty($matches['sum']) && !empty($context['sum'])) {
                return "Line $lineNumber can't have sum, because sum ({$context['sum']}) is already set in form";
            }
        }

        return null;
    }

    private static function validateCounts(array $rows): ?string
    {
        $countRows = count($rows);
        $countUsers = count(array_filter(array_column($rows, 'user_id')));
        $countSum = count(array_filter(array_column($rows, 'sum')));

        if ($countUsers < $countRows || $countUsers === 0 || $countRows === 0) {
            return 'empty strings not allowed';
        }

        if ($countRows > self::MAX_LINES) {
            return 'more than ' . static::MAX_LINES . ' lines';
        }

        if ($countSum > 0 && $countSum < $countRows) {
            return 'sum must be for every user';
        }

        return null;
    }

    private static function validateColumns(array $rows, self $form, array $context): ?string
    {
        $moneyValidator = new MoneyValidator();
        $bigIdValidator = new BigIdValidator();

        $uniqUsers = [];
        $totalErrors = [];
        foreach ($rows as ['line' => $line, 'user_id' => $userId, 'sum' => $sum]) {
            $error = $bigIdValidator->validate($userId, $form, $context);
            if ($error !== null) {
                $totalErrors[] = "Line $line id $userId: $error";
                continue;
            }

            if (isset($uniqUsers[$userId])) {
                $totalErrors[] = "Id $userId is duplicated on lines: {$uniqUsers[$userId]} and $line";
                continue;
            }
            $uniqUsers[$userId] = $line;

            $error = $moneyValidator->validate($sum, $form, $context);
            if ($sum !== null && $error !== null) {
                $totalErrors[] = "Line $line sum $sum $error";
            }
        }

        if (count($totalErrors) > 0) {
            return implode("\n", $totalErrors);
        }

        return null;
    }

    private static function validateUsersAndSetDetails(self $form, array $rows): ?string
    {
        if (!isset($form->siteId)) {
            return null;
        }

        $usersDetails = $form->getUsersDetails($rows, $form->siteId);

        $bblStatuses = [];
        if (property_exists($form, 'isAdvancedMode') && $form->isAdvancedMode === false) {
            $bblStatuses = [User::BONUS_BL_STATUS_YES_AUTO, User::BONUS_BL_STATUS_YES_MANUAL];
        }

        $totalErrors = [];
        $currency = null;
        foreach ($usersDetails as $user) {
            if (!$user['exists']) {
                $totalErrors[] = "Line {$user['line']}: user {$user['user_id']} does not exist";
                continue;
            }
            if ($user['currency'] === null) {
                $totalErrors[] = "Line {$user['line']}: user {$user['user_id']} has no currency";
                continue;
            }

            if (isset($currency)) {
                if ($currency !== $user['currency']) {
                    $totalErrors[] = "All users must have same currency";
                    continue;
                }
            } else {
                $currency = $user['currency'];
            }

            if (in_array($user['bbl_status'], $bblStatuses, true)) {
                $totalErrors[] = "Line {$user['line']}: user {$user['user_id']} is in bonus BL";
            }
        }

        if (count($totalErrors) > 0) {
            return implode("\n", $totalErrors);
        }

        $form->usersWithDetails = $usersDetails;
        return null;
    }

    public static function isSumRequired(mixed $value, self $form): ?string
    {
        if (!isset($form->userId) || $value !== null) {
            return null;
        }
        $sums = array_column($form->usersWithDetails, 'sum');
        return (count(array_filter($sums)) === 0) ? 'is required' : null;
    }

    private function getUsersDetails(array $rows, int $siteId): array
    {
        $valuesTable = Db::valuesTable($this->db, $rows, [
            'line' => 'int',
            'user_id' => 'bigint',
            'sum' => 'numeric',
        ], 't');

        return (new Query($this->db))
            ->select([
                't.line',
                't.user_id',
                't.sum',
                'exists' => '(u.user_id IS NOT NULL)',
                'currency' => 'ANY_VALUE(uw.currency)',
                'u.bbl_status',
            ])
            ->from($valuesTable)
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.user_id = t.user_id AND u.site_id = :siteId', ['siteId' => $siteId])
            ->leftJoin(['uw' => UserWallets::TABLE_NAME], 'uw.user_id = u.user_id AND uw.site_id = u.site_id AND ' . UserWallets::getActiveCondition('uw'))
            ->groupBy('t.line, t.user_id, t.sum, u.user_id, u.bbl_status')
            ->all();
    }

    private static function buildUsersSumsArray(?string $value): array
    {
        $result = [];
        foreach (Str::explodeText($value) as $lineNumber => $line) {
            $columns = preg_split("#\s+#", trim($line));
            $result[] = [
                'line' => $lineNumber + 1,
                'user_id' => $columns[0] ?? null,
                'sum' => $columns[1] ?? null,
            ];
        }
        return $result;
    }
}
