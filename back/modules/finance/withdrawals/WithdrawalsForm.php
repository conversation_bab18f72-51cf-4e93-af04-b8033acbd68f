<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawals;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\CurrencyValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\Rate;
use app\back\entities\User;
use app\back\entities\UserIgnoreId;
use app\back\entities\UserKyc;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\entities\Withdrawal;
use app\back\entities\WithdrawalComment;
use app\back\entities\WpProgramsTrafficType;
use app\back\modules\finance\components\Permutator;
use app\back\modules\finance\components\WithdrawalsRestrictionManager;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\reports\FinopOrders\FinopOrdersConfig;
use app\back\modules\reports\reports\CidUsers\CidUsersConfig;
use app\back\modules\user\spins\SpinsForm;
use app\back\repositories\Brands;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\Countries;
use app\back\repositories\Employees;
use app\back\repositories\PaySystems;
use app\back\repositories\Refcodes;
use app\back\repositories\Sites;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\UserKycs;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use app\back\repositories\WithdrawalComments;
use app\back\repositories\Withdrawals;
use app\back\repositories\WpProgramsTrafficTypes;
use app\back\repositories\WpWebmasterPrograms;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class WithdrawalsForm
{
    use RichTable;
    use WithdrawalsMoneyHelper;
    use AllowedSitesForWithdrawalsValidate;

    #[IdValidator]
    #[CallableValidator([self::class, 'allowedSitesForWithdrawalsValidate'])]
    public ?int $siteId = null;

    #[CallableValidator([self::class, 'allowedBrandsValidate'])]
    public ?array $brandId = [];

    #[IdValidator]
    public ?int $userId = null;

    #[StringValidator]
    public ?string $psName = null;

    #[StringValidator]
    public ?string $wallet = null;

    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    public ?int $userStatus = null;

    #[CurrencyValidator]
    public ?string $currency = null;

    #[StringValidator(2, 2)]
    public ?string $country = null;

    #[StringValidator(0, 1000)]
    public ?string $comment_withdraw = null;

    #[StringArrayValidator(UserKyc::KYC_STATUSES)]
    public ?array $kyc = [UserKyc::KYC_VERIFIED];

    #[IntArrayValidator(Withdrawal::DECISIONS)]
    public ?array $decision = null;

    private array $onlyAllowedBrands;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        public readonly WithdrawalsRestrictionManager $restrictionManager,
        private readonly ConnectionInterface $db,
        private readonly Sites $sites,
        private readonly Permutator $pm,
        private readonly BaseAuthAccess $bac,
        private readonly Brands $brands,
        private readonly Employees $employeesRepo,
        private readonly WpProgramsTrafficTypes $wpProgramsTrafficTypesRepo,
        private readonly Countries $countriesRepo,
    ) {
        $this->pageSize = 200;

        $this->onlyAllowedBrands = $this->allowedLists->onlyBrands();

        if (!empty($this->onlyAllowedBrands)) {
            $this->brandId = array_keys($this->onlyAllowedBrands);
        }
    }

    public function total(): int
    {
        return $this->getFilterQuery()
            ->groupBy([])
            ->select(['total' => 'COUNT(DISTINCT (u.site_id, u.user_id))'])
            ->scalar();
    }

    protected function blocks(): array
    {
        $siteOptions = $this->allowedLists->getSitesWithGroups(false, $this->allowedLists->sitesForWithdrawals());

        return [
            [
                $this->selectSiteCell(1, 'siteId', 'Site', array_merge(['multiple' => false], $siteOptions)),
                $this->selectSiteCell(1, 'brandId', 'Brand', [
                    'list' => empty($this->onlyAllowedBrands) ? $this->brands->getIdNameDict() : Arr::assocToIdName($this->onlyAllowedBrands),
                ]),
                $this->textInputCell(1, 'userId', 'User'),
                $this->selectCell(1, 'userStatus', 'User status', ['multiple' => false, 'list' => Arr::assocToIdName(User::actualStatuses())]),
                $this->textInputLiveSearchCell(1, 'psName', 'Pay sys', '/dictionaries/filtered/pay-systems'),
                $this->textInputCell(1, 'wallet', 'Requisite'),
                $this->selectCell(1, 'currency', 'Currency', ['multiple' => false, 'list' => Arr::assocToIdName(Rate::currencies())]),
                $this->selectCell(1, 'country', 'Country', [ 'multiple' => false, 'list' => Arr::assocToIdName($this->countriesRepo->getNames())]),
                $this->textInputCell(2, 'comment_withdraw', 'Comment'),
                $this->selectCell(1, 'kyc', 'KYC', ['multiple' => true, 'list' => Arr::assocToIdName(UserKyc::KYC_STATUSES)]),
                $this->selectCell(1, 'decision', 'Decision', ['multiple' => true, 'list' => Arr::assocToIdName(Withdrawal::DECISIONS)]),
            ],
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Site', 'code' => 'siteName', 'sortExpr' => 'u.site_id'],
            ['name' => 'User', 'code' => 'userId', 'slotName' => 'userId', 'sortExpr' => 'u.user_id'],
            ['name' => 'User status', 'code' => 'userFullStatus', 'sortExpr' => 'u.status'],
            ['name' => 'KYC', 'code' => 'kyc', 'sortExpr' => 'ANY_VALUE(kyc.kyc_status)'],
            ['name' => 'Links', 'slotName' => 'userLinks'],
            ['name' => 'Balance', 'code' => 'balance', 'slotName' => 'balance', 'sortExpr' => 'ANY_VALUE(uwr.balance)', 'nowrap' => true],
            ['name' => 'Sum', 'code' => 'sumEur', 'slotName' => 'withdrawSum', 'sortable' => true, 'nowrap' => true],
            ['name' => 'In / Out (€)', 'slotName' => 'inOut', 'nowrap' => true],
            ['name' => 'Pay sys', 'code' => 'psName'],
            ['name' => 'Requisite', 'code' => 'wallet', 'slotName' => 'wallet', 'sortExpr' => 'wallet'],
            ['name' => 'Sel', 'slotName' => 'select'],
            ['name' => 'Withdraw', 'slotName' => 'withdraw', 'style' => ['width' => '9rem']],
            ['name' => 'Comment', 'slotName' => 'comment', 'align' => 'start', 'style' => ['max-width' => '15rem']],
            ['name' => 'Deny', 'slotName' => 'btnDeny'],
        ];
    }

    public static function allowedBrandsValidate(?array $value, self $form, array $context): ?string
    {
        if ($value !== null && !empty($form->onlyAllowedBrands)) {
            return (new IntArrayValidator($form->onlyAllowedBrands, allowEmpty: false))->validate($value, $form, $context);
        }

        return null;
    }

    /** Уже обработанные заявки за сутки сгрупированные по юзерам */
    private function getWithdrawalsAggregatedQuery(string $fTable): Query
    {
        $filterRegularWithdrawals = 'FILTER (WHERE w.created_at > :midnight AND w.status IN (:status_new, :status_synced))';
        $filterPlannedWithdrawals = 'FILTER (WHERE w.status = :status_planned AND us.status = :stat_status_new)';

        return (new Query($this->db))
            ->select([
                'us.site_id',
                'us.user_id',
                'sumAllowed' => "SUM(CASE WHEN w.decision=:allow THEN us.amount_orig ELSE NULL END) $filterRegularWithdrawals",
                'countAllowed' => "COUNT(CASE WHEN w.decision=:allow THEN 1 ELSE NULL END) $filterRegularWithdrawals",
                'sumDenied' => "SUM(CASE WHEN w.decision=:deny THEN us.amount_orig ELSE NULL END) $filterRegularWithdrawals",
                'countDenied' => "COUNT(CASE WHEN w.decision=:deny THEN 1 ELSE NULL END) $filterRegularWithdrawals",
                'plannedTransactionIds' => "JSON_AGG(DISTINCT us.transaction_id) $filterPlannedWithdrawals",
                'plannedWithdrawalIds' => "JSON_AGG(DISTINCT w.id) $filterPlannedWithdrawals",
            ])
            ->from(['w' => Withdrawals::TABLE_NAME])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = w.site_id and us.transaction_id = w.transaction_id')
            ->where([
                'AND',
                "w.site_id = $fTable.site_id AND w.user_id = $fTable.user_id",
                'w.created_at > :midnight OR w.planned_at > :midnight',
            ])
            ->groupBy(['us.site_id', 'us.user_id'])
            ->addParams([
                'allow' => Withdrawal::DECISION_ALLOW,
                'deny' => Withdrawal::DECISION_DENY,
                'status_new' => Withdrawal::STATUS_NEW,
                'status_synced' => Withdrawal::STATUS_SYNCED,
                'status_planned' => Withdrawal::STATUS_PLANNED,
                'midnight' => date("Y-m-d H:i:s", strtotime("midnight")),
                'stat_status_new' => UserTransaction::STATUS_NEW,
            ]);
    }

    /** Актуальные заявки */
    private function getFilterQuery(): Query
    {
        $walletRealSubQuery = (new Query($this->db))
            ->select([
                'balance' => 'SUM(balance)',
                'currency' => "STRING_AGG(currency, ',')"
            ])
            ->from(['uw' => UserWallets::TABLE_NAME])
            ->where(['type' => UserWallet::TYPE_REAL])
            ->andWhere('uw.site_id = u.site_id AND uw.user_id = u.user_id');

        $query = (new Query($this->db))
            ->select([
                'site_id' => 'u.site_id', // Snake case for easier join condition https://stackoverflow.com/questions/20878932/are-postgresql-column-names-case-sensitive
                'user_id' => 'u.user_id',
                'refcode_id' => 'u.refcode_id', // Only for further join
                'brandId' => 'u.brand_id',
                'kyc' => 'ANY_VALUE(kyc.kyc_status)',
                'userFullStatus' => User::getFullStatusExpression(),
                'u.cid',
                'balanceOrig' => 'ANY_VALUE(uwr.balance)',
                'currency' => 'ANY_VALUE(uwr.currency)',
                'lastNewWdAt' => 'MAX(us.created_at)',
                'psName' => "STRING_AGG(DISTINCT cp.name, ', ')",
                'wallet' => "STRING_AGG(DISTINCT us.wallet, ', ')",
                'sumOrig' => 'SUM(us.amount_orig)',
                'sumEur' => 'SUM(us.amount_eur)',
                'countTotal' => 'COUNT(*)',
                'commentAdmin' => 'JSON_AGG(DISTINCT us.comment_admin)',
                'commentWithdraw' => 'JSON_AGG(DISTINCT wc.comment)',
                'commentWithdrawUpdatedBy' => 'JSON_AGG(DISTINCT wc.updated_by)',
                'statsCurrency' => "STRING_AGG(DISTINCT us.currency, ', ')",
                'combinationAmounts' => 'JSONB_OBJECT_AGG(us.transaction_id, us.amount_orig::TEXT)',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = us.site_id and u.user_id = us.user_id')
            ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
            ->leftJoin(['cps' => CanonicalPaySySources::TABLE_NAME], 'cps.name = ps.name AND cps.source = :source', ['source' => CanonicalPaySySource::SOURCE_PRODUCT])
            ->leftJoin(['cp' => CanonicalPaySystems::TABLE_NAME], 'cp.id = cps.canonical_pay_sys_id')
            ->leftJoin(['wc' => WithdrawalComments::TABLE_NAME], 'wc.site_id = us.site_id AND wc.user_id = us.user_id AND wc.transaction_id = us.transaction_id')
            ->leftJoin(
                ['w' => Withdrawals::TABLE_NAME],
                'w.site_id = us.site_id AND w.transaction_id = us.transaction_id AND w.status IN (:new, :synced)',
                [
                    'new' => Withdrawal::STATUS_NEW,
                    'synced' => Withdrawal::STATUS_SYNCED,
                ]
            )
            ->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id')
            ->join("LEFT JOIN LATERAL", ['uwr' => $walletRealSubQuery], 'true')
            ->where([
                'us.status' => UserTransaction::STATUS_NEW,
                'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                'us.dir' => UserTransaction::DIR_OUT,
                'us.site_id' => array_keys($this->allowedLists->sitesForWithdrawals()),
                'w.id' => $this->decision ?: null,
            ])
            ->andFilterWhere([
                'AND',
                [
                    'u.site_id' => $this->siteId,
                    'u.brand_id' => $this->brandId,
                    'u.user_id' => $this->userId,
                    'u.status' => $this->userStatus,
                    'u.country' => $this->country,
                    'uwr.currency' => $this->currency,
                ],
                ['ILIKE', 'cp.name', $this->psName],
                ['ILIKE', 'us.wallet', $this->wallet],
                ['ILIKE', 'wc.comment', $this->comment_withdraw],
            ]);

        if ($this->kyc) {
            $query->andWhere(Db::filterWithDefault('kyc.kyc_status', $this->kyc, UserKyc::KYC_NOT_VERIFIED));
        }

        return $query;
    }

    private function getCommentsQuery(string $fTable): Query
    {
        return (new Query($this->db))
            ->select([
                'c.comment',
                'c.updated_by',
            ])
            ->from(['c' => WithdrawalComments::TABLE_NAME])
            ->where(['AND',
                "c.site_id = $fTable.site_id",
                "c.user_id = $fTable.user_id",
                'c.transaction_id IS NULL',
                ['c.source' => [WithdrawalComment::SOURCE_ANALYTICS, WithdrawalComment::SOURCE_VIPAFF]],
            ])
            ->orderBy(['c.updated_at' => SORT_DESC])
            ->limit(1);
    }

    private function getQuery(): Query
    {
        $usa = $this->getFilterQuery()
            ->groupBy(['u.site_id', 'u.user_id'])
            ->orderBy($this->getOrderMap())
            ->addOrderBy(['MAX(us.created_at)' => SORT_DESC])
            ->limit($this->getLimit())
            ->offset($this->getOffset());

        return (new Query($this->db))
            ->withQuery($usa, 'usa')
            ->select([
                'siteId' => 'usa.site_id',
                'userId' => 'usa.user_id',
                'usa.*',
                'wa.*',
                'depLtEur' => 'FLOOR(usi.dep_lt_eur)',
                'wdLtEur' => 'FLOOR(usi.wd_lt_eur)',
                'hasBlockedRequisites' => 'usi.has_blocked_requisites',
                'userCommentWithdraw' => 'wc.comment',
                'userCommentWithdrawUpdatedBy' => 'wc.updated_by',
                'isIgnored' => '(ui.user_id IS NOT NULL)',
                'ignoreComment' => 'ui.comment',
                'ignoreSource' => 'ui.source',
                'trafficTypeId' => 'wp_wp.traffic_type_id',
            ])
            ->from('usa')
            ->join("LEFT JOIN LATERAL", ['wa' => $this->getWithdrawalsAggregatedQuery('usa')], 'true')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = usa.site_id and usi.user_id = usa.user_id')
            ->leftJoin(['ui' => UserIgnoreIds::TABLE_NAME], 'ui.site_id = usa.site_id and ui.user_id = usa.user_id')
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = usa.refcode_id')
            ->leftJoin(['wp_wp' => WpWebmasterPrograms::TABLE_NAME], Refcodes::wpJoinExpression('wp_wp.webmaster_id', 'r', 'wp_wp.program_id = ' . Refcodes::programExpression()))
            ->join("LEFT JOIN LATERAL", ['wc' => $this->getCommentsQuery('usa')], 'true');
    }

    public function data(): array
    {
        $result = $this->getQuery()->all();

        array_walk($result, function (&$el) {
            unset($el['lastNewWdAt'], $el['site_id'], $el['user_id'], $el['refcode_id']);
            $el['siteName'] = $this->sites->getShortNameById($el['siteId']);
            $el['siteUser'] = $el['siteName'] . '-' . $el['userId'];

            if (!empty($el['brandId'])) {
                $brandName = $this->brands->getNameById($el['brandId']);
                $el['siteName'] .= " ($brandName)";
            }

            $el['links'] = $this->getUserLinks($el['siteId'], $el['userId'], $el['cid']);
            if ($el['wallet']) {
                $el['walletReportUrl'] = FinopOrdersConfig::url([
                    'filters' => [
                        ['site_id', [$el['siteId']], Operators::IN],
                        ['requisite', implode("\n", explode(', ', $el['wallet'])), Operators::IN],
                        ['date', date('Y-m-d', strtotime('-2 weeks')), Operators::GE],
                    ],
                    'columns' => ['status_id', 'date_created', 'date', 'order_id', 'invoice_id', 'site_id', 'user_id', 'amount', 'currency', 'type', 'status', 'card_holder', 'requisite', 'country_bin', 'country_real'],
                ]);
            }

            $el['balance'] = $this->formatMoney($el['balanceOrig'], $el['currency']);
            unset($el['balanceOrig']);
            $commentAdmin = Json::decode($el['commentAdmin']) ?? [];
            $commentWithdraw = array_filter(Json::decode($el['commentWithdraw']) ?? []);
            if ($commentWithdraw) {
                $comment = $commentWithdraw;
            } else {
                $comment = $commentAdmin;
            }
            if ($el['isIgnored']) {
                if (empty($el['ignoreComment'])) {
                    $el['ignoreComment'] = 'Ignored user';
                }
                $el['ignoreComment'] .= ' (Source: ' . UserIgnoreId::getSourceNameById($el['ignoreSource']) . ')';
                unset($el['ignoreSource']);
            }
            $el['sumFull'] = $this->formatMoney($el['sumOrig'], $el['statsCurrency']);
            unset($el['sumOrig']);
            $el['comment'] = implode(', ', array_unique($comment));

            $commentWithdrawUpdatedBy = array_filter(Json::decode($el['commentWithdrawUpdatedBy']) ?? []);
            if ($commentWithdrawUpdatedBy) {
                $el['commentWithdrawUpdatedBy'] = implode(', ', array_map(
                    static fn($el) => Str::emailToLdap($el),
                    $this->employeesRepo->getNamesByIds(array_unique($commentWithdrawUpdatedBy))
                ));
            } else {
                $el['commentWithdrawUpdatedBy'] = '';
            }

            $el['kyc'] = $el['kyc'] !== null ? UserKyc::getKycStatusById($el['kyc']) : null;
            $el['isWdGreaterThanDep'] = $el['wdLtEur'] + $el['sumEur'] > $el['depLtEur'];
            unset($el['sumEur']);
            $el['wdLtEur'] = $this->formatMoney($el['wdLtEur']);
            $el['depLtEur'] = $this->formatMoney($el['depLtEur']);
            if ($el['trafficTypeId'] === $this->wpProgramsTrafficTypesRepo->getIdByName(WpProgramsTrafficType::TT_SCHEME_PAYMENT_RESTRICTION)) {
                $el['userCommentWithdraw'] = WpProgramsTrafficType::TT_SCHEME_PAYMENT_RESTRICTION;
                $el['userCommentWithdrawUpdatedBy'] = '';
            } else {
                $el['userCommentWithdrawUpdatedBy'] = Str::emailToLdap($this->employeesRepo->getNameById($el['userCommentWithdrawUpdatedBy']));
            }
            unset($el['trafficTypeId']);
        });

        $this->appendStatsDetails($result);

        return $result;
    }

    private function appendStatsDetails(&$rows): void
    {
        $siteIds = array_unique(Arr::getColumnAssoc($rows, 'siteId'));
        $allowedSites = array_map(fn($siteId) => $this->restrictionManager->isWithdrawalAllowed($siteId, $this->bac->can(Permission::PERM_WITHDRAW_OVER_LIMIT)), $siteIds);
        $allowedSites = array_combine($siteIds, $allowedSites);

        foreach ($rows as &$row) {
            $processable = $row['currency'] === $row['statsCurrency'];
            $row['canAllow'] = $processable && $allowedSites[$row['siteId']];
            $row['canDeny'] = $processable;
            $row['plannedWithdrawalIds'] = Json::decode($row['plannedWithdrawalIds'] ?? '[]');
            $row['plannedTransactionIds'] = Json::decode($row['plannedTransactionIds'] ?? '[]');
            $this->fillSumCombinations($row);
        }
    }

    private function fillSumCombinations(array &$row): void
    {
        $row['combinations'] = [];
        $transactions = $plannedStats =  [];
        foreach (Json::decode($row['combinationAmounts']) as $transactionId => $sum) {
            if (in_array($transactionId, $row['plannedTransactionIds'], false)) {
                $plannedStats[$transactionId] = $sum;
            } else {
                $transactions[] = [
                    'transaction_id' => $transactionId,
                    'amount_orig' => $sum,
                ];
            }
        }
        unset($row['combinationAmounts']);

        $row['plannedStats'] = $plannedStats;

        if (empty($transactions)) {
            return;
        }

        foreach ($this->pm->getSumCombinations($transactions) as $amount => $ids) {
            $row['combinations'][] = [
                'id' => implode(',', $ids),
                'name' => $this->formatMoney((string)$amount)
            ];
        }
    }

    private function getUserLinks($siteId, $userId, $cid): array
    {
        $result = [];
        if ($cid) {
            $result[] = [
                'href' => CidUsersConfig::cidUsersCloudHref($cid, ['cid', 'site_id', 'brand_id', 'user_id', 'reg_date', 'emails', 'requisites', 'balance', 'in', 'in_out', 'comment']),
                'icon' => 'icn-cloud',
            ];
        }
        $result[] = [
            'href' => User::cardHref($siteId, $userId),
            'icon' => 'icn-user',
        ];
        $result[] = [
            'href' => SpinsForm::userHref($siteId, $userId, SpinsForm::SOURCE_PRODUCT),
            'icon' => 'icn-chart',
        ];

        return $result;
    }
}
