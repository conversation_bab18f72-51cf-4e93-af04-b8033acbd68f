<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\bonusOffers;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\BonusOffer;
use app\back\entities\Site;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\modules\user\player\forms\BasePlayerForm;
use app\back\repositories\BonusOffers;
use app\back\repositories\Sites;

class BonusOfferCreateForm extends BasePlayerForm
{
    use FormGrid;

    #[IntValidator]
    public int $minDepSum;
    #[IntValidator]
    public int $maxPrize;
    #[IntInArrayValidator([BonusOffer::class, 'getActivationTimeVariants'])]
    public int $activationTime;
    #[IntInArrayValidator([BonusOffer::class, 'getActivationTimeVariants'])]
    public ?int $availableTime = null;
    #[IntInArrayValidator([BonusOffer::class, 'getPrizePercents'])]
    public int $prizePercent = 15;
    #[IntValidator(10)]
    public int $wager = 10;
    #[IntValidator(BonusOffer::MIN_MAX_TRANSFER, BonusOffer::MAX_MAX_TRANSFER)]
    public ?int $maxTransfer = null;
    #[IntValidator]
    public ?int $maxWageringAmount = null; // GI ONLY
    #[IntValidator]
    public ?int $wageringHours = null; // SMEN ONLY
    #[CallableValidator([self::class, 'allowAddValidator'])]
    #[BooleanValidator(true)]
    public int $needActivate = 1;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BonusOffers $bonusOffersRepo,
        private readonly Sites $sitesRepo,
        private readonly BaseAuthAccess $auth,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    protected function title(): string
    {
        return 'Add bonus offer';
    }

    protected function blocks(): array
    {
        $isSmen = Site::getPlatformBySiteId($this->siteId) === Site::PLATFORM_SMEN;

        $blocks = [
            [
                $this->textInputCell(4, 'minDepSum','Min dep sum'),
                $this->textInputCell(4, 'maxPrize','Max prize'),
                $this->selectCell(4, 'activationTime','Activation time', [
                    'multiple' => false,
                    'list' => Arr::assocToIdName(BonusOffer::getActivationTimeVariants()),
                ]),
            ],
            [
                $this->selectCell(4, 'prizePercent', 'Prize percent', [
                    'multiple' => false,
                    'list' => Arr::assocToIdName(BonusOffer::getPrizePercents()),
                ]),
                $this->textInputCell(4, 'wager','Wager'),
                $isSmen ? $this->textInputCell(4,'wageringHours','Wager hours') : $this->textInputCell(4,'maxWageringAmount','Max wagering'),
            ],
        ];

        $row = [
            $this->textInputCell($isSmen ? 4 : 10, 'maxTransfer', 'Max Transfer', ['input-type' => 'number']),
        ];

        if ($isSmen) {
            $row[] = $this->selectCell(4, 'availableTime', 'Available time', [
                'multiple' => false,
                'list' => Arr::assocToIdName(BonusOffer::getActivationTimeVariants()),
            ]);
            $row[] = $this->checkboxCell(2, 'needActivate', 'Activate');
        }

        $row[] = $this->submitCell(2, 'Add', ['buttonStyle' => 'btn-success', 'buttonIcon' => 'icn-plus']);
        $blocks[] = $row;

        return $blocks;
    }

    public function process(): void
    {
        $boParams = match (Site::getPlatformBySiteId($this->siteId)) {
            Site::PLATFORM_SMEN => $this->initSmen(),
            default => $this->initOther(),
        };

        $bo = $this->bonusOffersRepo->validateAndCreate([
            ... $boParams,
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
            'deposit_sum_min' => $this->minDepSum,
            'prize_sum_max' => $this->maxPrize,
            'activation_time' => $this->activationTime,
            'available_time' => $this->availableTime ?: $this->activationTime,
            'prize_percent' => $this->prizePercent,
            'wager' => $this->wager,
            'status' => $this->needActivate ? BonusOffer::STATUS_ACTIVE : BonusOffer::STATUS_AVAILABLE,
            'operator_id' => $this->auth->employeeId(),
            'max_transfer' => $this->maxTransfer,
            'max_wagering_amount' => $this->maxWageringAmount,
            'wager_hours' => $this->wageringHours,
            'need_activate' => $this->needActivate,
        ]);

        $this->bonusOffersRepo->insert($bo);
    }

    private function initOther(): array
    {
        return [
            'created_at' => new \DateTimeImmutable(BonusOffer::SQL_NOW_DATETIME),
            'updated_at' => new \DateTimeImmutable(BonusOffer::SQL_NOW_DATETIME),
        ];
    }

    private function initSmen(): array
    {
        $fetchTask = $this->requestFactory->createFetchTask('bonus-offer-create', $this->siteId, [
            'params' => [
                'minDepSum' => $this->minDepSum,
                'maxPrize' => $this->maxPrize,
                'activationTime' => $this->activationTime,
                'availableTime' => $this->availableTime ?: $this->activationTime,
                'prizePercent' => $this->prizePercent,
                'wager' => $this->wager,
                'wageringHours' => $this->wageringHours,
                'wageringMaxTransferMultiplier' => $this->maxTransfer,
                'userId' => $this->userId,
                'operatorEmail' => $this->auth->employee()->email,
                'needActivate' => $this->needActivate,
            ],
        ]);

        $result = $fetchTask->finalData();
        $row = $result[0];

        return [
            'remote_id' => $row['remote_id'],
            'created_at' => new \DateTimeImmutable($row['created_at']),
            'updated_at' => new \DateTimeImmutable($row['updated_at']),
        ];
    }

    public static function allowAddValidator($value, self $form): ?string
    {
        $hasOffer = BonusOffers::userHasOffer($form->bonusOffersRepo->db, $form->siteId, $form->userId);

        if ($hasOffer && $value === 1) {
            return 'User already has active offer';
        }

        return null;
    }
}
