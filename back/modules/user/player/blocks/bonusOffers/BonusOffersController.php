<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\bonusOffers;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\modules\user\player\blocks\BasePlayerBlockController;

#[AccessCheckPage]
class BonusOffersController extends BasePlayerBlockController
{
    public function actionAddForm(BonusOfferCreateForm $form, Request $request): array
    {
        $form->validateOrException($request->json(), ['siteId']);
        return $form->response();
    }

    public function actionAdd(BonusOfferCreateForm $form, Request $request, SessionMessages $messages): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->process();
        $messages->success('Bonus offer successfully added');
        $this->bl()->create($data);
    }

    public function actionCancel(BonusOfferCancelForm $form, Request $request, SessionMessages $messages): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->cancel();
        $messages->success('Bonus offer canceled');
        $this->bl()->delete($data);
    }

    public function actionDeactivate(BonusOfferDeactivateForm $form, Request $request, SessionMessages $messages): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->deactivate();
        $messages->success('Bonus offer deactivated');
        $this->bl()->delete($data);
    }
}
