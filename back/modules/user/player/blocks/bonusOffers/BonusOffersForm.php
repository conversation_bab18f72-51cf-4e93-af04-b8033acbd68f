<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\bonusOffers;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\entities\BonusOffer;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\BonusOffers;
use app\back\repositories\Employees;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class BonusOffersForm extends BasePlayerForm
{
    use RichTable {
        validateAndResponse as innerValidateAndResponse;
    }

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly Users $usersRepo,
        private readonly Sites $sitesRepo,
    ) {
        parent::__construct($allowedLists, $db, $auth);
    }

    protected function blocks(): array
    {
        return [];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Created At', 'code' => 'created_at'],
            ['name' => 'Act. Time', 'code' => 'activation_time'],
            ['name' => 'Time Left', 'code' => 'left_time'],
            ['name' => 'Status', 'code' => 'status'],
            ['name' => 'Min Dep Sum', 'code' => 'deposit_sum_min'],
            ['name' => 'Actual Dep Sum', 'code' => 'deposit_sum_actual'],
            ['name' => 'Max Prize', 'code' => 'prize_sum_max'],
            ['name' => 'Prize %', 'code' => 'prize_percent'],
            ['name' => 'Wager', 'code' => 'wager'],
            ['name' => 'Prize Sum', 'code' => 'prize_sum'],
            ['name' => 'Maх Transfer', 'code' => 'max_transfer'],
            Site::getPlatformBySiteId($this->siteId) === Site::PLATFORM_SMEN ? ['name' => 'Wager Hours', 'code' => 'wager_hours'] : ['name' => 'Max Wagering', 'code' => 'max_wagering_amount'],
            ['name' => 'Operator', 'code' => 'operator_email'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function data(): array
    {
        $sumQuery = (new Query($this->db))
            ->select([
                'bo.id',
                'deposit_sum_actual' => 'SUM(us.amount_orig)'
            ])
            ->from(['bo' => BonusOffers::TABLE_NAME])
            ->leftJoin(
                ['us' => UserTransactions::TABLE_NAME],
                [
                    'AND',
                    'us.site_id = bo.site_id',
                    'us.user_id = bo.user_id',
                    ['us.op_id' => UserTransaction::OP_IN],
                    ['us.status' => UserTransaction::STATUS_SUCCESS],
                    'us.created_at >= bo.created_at',
                    "us.created_at < COALESCE(bo.condition_met_at, bo.created_at + (bo.activation_time || ' hours')::interval)",
                ]
            )
            ->where([
                'AND',
                ['bo.site_id' => $this->siteId],
                ['bo.user_id' => $this->userId],
            ])
            ->groupBy('bo.id');

        $data = (new Query($this->db))
            ->select([
                'bo.id',
                'bo.created_at',
                'bo.activation_time',
                'left_time' => "CASE WHEN bo.status = :status_active THEN AGE(bo.created_at + (bo.activation_time || ' hours')::interval, NOW()::timestamp(0)) END",
                'bo.status',
                'bo.deposit_sum_min',
                'bo.prize_sum_max',
                'bo.prize_percent',
                'bo.wager',
                'bo.prize_sum',
                'bo.max_transfer',
                'bo.max_wagering_amount',
                'bo.wager_hours',
                'sq.deposit_sum_actual',
                'operator_email' => 'e.email',
            ])
            ->from(['bo' => BonusOffers::TABLE_NAME])
            ->leftJoin(['sq' => $sumQuery], 'sq.id = bo.id')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = bo.operator_id')
            ->where($this->siteUser('bo'))
            ->orderBy('bo.created_at desc')
            ->addParams(['status_active' => BonusOffer::STATUS_ACTIVE])
            ->all();

        $isSmen = Site::getPlatformBySiteId($this->siteId) === Site::PLATFORM_SMEN;
        $perm = $this->auth->can(Permission::PERM_CREATE_BONUS_OFFERS);
        array_walk($data, static function (&$row) use ($perm, $isSmen) {
            $row['allowCancel'] = $perm && in_array((int)$row['status'], [BonusOffer::STATUS_ACTIVE, BonusOffer::STATUS_AVAILABLE],true);
            $row['allowDeactivate'] = $isSmen && $perm && (int)$row['status'] === BonusOffer::STATUS_ACTIVE;
            $row['activation_time'] = BonusOffer::getActivationTime($row['activation_time']);
            $row['status'] = BonusOffer::getStatus($row['status']);
            $row['prize_percent'] = BonusOffer::getPrizePercent($row['prize_percent']);
        });

        return $data;
    }

    public function total(): int
    {
        return (int)(new Query($this->db))
            ->select(['COUNT(*)'])
            ->from(['bo' => BonusOffers::TABLE_NAME])
            ->where($this->siteUser())
            ->scalar();
    }

    public function validateAndResponse(array $requestData): array
    {
        $response = $this->innerValidateAndResponse($requestData);
        $allowAdd = $this->auth->can(Permission::PERM_CREATE_BONUS_OFFERS) && !$this->usersRepo->isUserBlacklisted($this->siteId, $this->userId);
        $response['allowAdd'] = $allowAdd;

        return $response;
    }
}
