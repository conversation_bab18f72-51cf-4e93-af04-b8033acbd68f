<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\adsCredentials;

use app\back\components\Form;
use app\back\components\SessionMessages;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntervalValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\AdCredential;
use app\back\entities\Task;
use app\back\entities\TaskQueue;
use app\back\repositories\AdCredentials;
use app\back\repositories\TaskQueues;
use app\back\repositories\Tasks;

class AdsCredentialsReloadForm
{
    use Form;

    private const string DATE_INTERVAL_PATTERN = '#^(today|yesterday|-\d+\s[a-z]+|\d{4}-\d{2}-\d{2}),(P\d+[DMWY])$#';

    private const string MIN_RELOAD_INTERVAL = 'P1D';
    private const string MAX_RELOAD_INTERVAL = 'P90D';

    #[IdValidator]
    #[CallableValidator([self::class, 'validateId'])]
    public int $id;
    #[StringValidator(9, 100)]
    #[MatchValidator(self::DATE_INTERVAL_PATTERN)]
    #[CallableValidator([self::class, 'validateDateIntervalString'])]
    public string $value;

    private string $from;
    private string $period;

    public function __construct(
        public SessionMessages $messages,
        private readonly AdCredentials $adCredentialsRepo,
        private readonly Tasks $tasks,
        private readonly TaskQueues $taskQueues,
    ) {
    }

    public static function validateId(int $value, self $form): ?string
    {
        /** @var AdCredential $credential */
        $credential = $form->adCredentialsRepo->findOne(['id' => $value]);

        if ($credential === null) {
            return "Can't reload. Credential not found";
        }

        if ($credential->is_active === false) {
            return "Can't reload. Credential is inactive";
        }

        return null;
    }

    public static function validateDateIntervalString(string $value, self $form, array $context): ?string
    {
        [$date, $period] = explode(',', $value);

        $date = strtotime($date);
        if ($date === false || $date < strtotime('-10 years') || $date > strtotime('today')) {
            return 'Invalid date';
        }

        $periodErrors = (new IntervalValidator(true, self::MIN_RELOAD_INTERVAL, self::MAX_RELOAD_INTERVAL))->validate($period, $form, $context);
        if ($periodErrors !== null) {
            return $periodErrors;
        }

        $endDate = (new \DateTime(date('Y-m-d', $date)))->add(new \DateInterval($period));
        if ($endDate > (new \DateTime('today'))) {
            return 'period in future is forbidden';
        }

        $form->from = date('Y-m-d', $date);
        $form->period = $period;

        return null;
    }

    public function reload(): void
    {
        /** @var AdCredential $entity */
        $entity = $this->adCredentialsRepo->findOneOr404(['id' => $this->id]);
        [$taskName, $resource] = AdCredential::PLATFORM_TASK_RESOURCE_MAP[$entity->platform_id];

        $taskEntity = $this->tasks->create(Task::SOURCE_RELOAD, $taskName, $resource, new \DateTimeImmutable($this->from), new \DateInterval($this->period), [$this->id]);
        $taskQueue = $this->taskQueues->create($taskEntity->id, TaskQueue::QUEUE_MANUAL);

        $this->messages->success("Reload {$this->from} {$this->period} added to schedule queue ({$taskQueue->id}). It can take about 3-5 minutes to finish");
    }
}
