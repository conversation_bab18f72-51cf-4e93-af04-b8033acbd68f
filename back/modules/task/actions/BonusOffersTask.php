<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\entities\BonusOffer;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\BonusOffers;
use app\back\repositories\Employees;

class BonusOffersTask extends ImportTask
{
    use TaskWithFromToRequest;

    private const array STATUSES_MAP = [
        'Active' => BonusOffer::STATUS_ACTIVE,
        'Met' => BonusOffer::STATUS_MET,
        'Expired' => BonusOffer::STATUS_EXPIRED,
        'Cancel' => BonusOffer::STATUS_CANCELLED,
        'Available' => BonusOffer::STATUS_AVAILABLE,
    ];

    public function __construct(
        private readonly BonusOffers $bonusOffersRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Employees $employeesRepo
    ) {
    }

    protected function repository(): BonusOffers
    {
        return $this->bonusOffersRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        $row['status'] = self::STATUSES_MAP[$row['status_name']];
        unset($row['status_name']);

        $row['operator_id'] = $this->employeesRepo->getIdByName($row['operator_email']);
        unset($row['operator_email']);

        foreach (['deposit_sum_min', 'prize_sum_max', 'prize_sum'] as $k) {
            $row[$k] = round((float) $row[$k]);
        }

        if ($row['prize_sum'] === 0) {
            $row['prize_sum'] = null;
        }

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, [], '(site_id, remote_id)');
    }
}
