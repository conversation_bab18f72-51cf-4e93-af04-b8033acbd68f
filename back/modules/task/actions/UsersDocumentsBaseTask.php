<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\BaseAuthAccess;
use app\back\components\DbUpsertResult;
use app\back\components\helpers\Arr;
use app\back\components\helpers\ImageHelper;
use app\back\components\helpers\Str;
use app\back\components\HttpClient;
use app\back\components\Initializable;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserDocuments;
use Yiisoft\Db\Query\Query;

abstract class UsersDocumentsBaseTask extends ImportTask
{
    use TaskWithFromToRequest;

    public const string MIME_TYPE_JPG = 'image/jpeg';
    public const string MIME_TYPE_PNG = 'image/png';
    public const string MIME_TYPE_PDF = 'application/pdf';
    protected const array MIME_TYPES_ALLOWED = [
        self::MIME_TYPE_JPG,
        self::MIME_TYPE_PNG,
        self::MIME_TYPE_PDF
    ];

    protected array $downloadUrls = [];
    protected bool $modeNoDownload = false;
    protected UserDocument $lastUploadedDocument;
    private ?string $rawBrokenImage = null;

    public function __construct(
        protected readonly SiteUserBuilder $siteUserBuilder,
        protected readonly HttpClient $httpClient,
        protected TaskSiteIdResolver $siteIdResolver,
        private readonly UserDocuments $userDocumentsRepo,
        private readonly FileStorage $storage,
        protected readonly BaseAuthAccess $auth,
    ) {
    }

    abstract protected function getDownloadLink(array $row): string;

    #[Initializable]
    final public function initMode(): void
    {
        $this->modeNoDownload = $this->mode === 'noDownload';
    }

    protected function repository(): BaseRepository
    {
        return $this->userDocumentsRepo;
    }

    public function process(): void
    {
        try {
            parent::process();
        } catch (\Throwable $e) {
            if (isset($this->lastUploadedDocument) && !isset($this->lastUploadedDocument->id)) {
                $this->storage->delete($this->lastUploadedDocument->storagePath())
                    ? $this->log->notice("Cleanup storage: removed {$this->lastUploadedDocument->storagePath()}")
                    : $this->log->error("Unable to cleanup storage: file not removed {$this->lastUploadedDocument->storagePath()}");
            }
            throw $e;
        }
    }

    protected function beforeFind(array &$row): bool
    {
        $row['filename'] = uniqid('', false) . '.jpg';
        $row['external_approve_required'] = true;
        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }

    protected function normalizeAndSaveImage(UserDocument $doc, string $imageBlob): bool
    {
        $documentInfo = $this->documentInfo($doc);

        try {
            [$width, $height] = getimagesizefromstring($imageBlob);
            if (max($width, $height) > 16384) {
                $this->log->warning("Skip large resolution $width*$height $documentInfo");
                return false;
            }
        } catch (\Throwable) {
            $this->log->warning("Skip corrupted. Unable to checks image resolution $documentInfo");
        }
        try {
            $imagick = new \Imagick();
            $imagick->readImageBlob($imageBlob);
            $imagick->setImageFormat('jpeg');
            if (!$this->beforeSaveFile($doc, $imagick)) {
                return false;
            }
            // important to clear rotation before save width & height
            ImageHelper::clearRotation($imagick);
            $doc->width = $imagick->getImageWidth();
            $doc->height = $imagick->getImageHeight();
            $doc->etag = md5($imagick->getImageBlob());
            $this->saveToStorage($doc, $imagick);
            $this->lastUploadedDocument = $doc;
        } catch (\ImagickException $e) {
            $this->log->warning("Skip corrupted '{$e->getMessage()}' $documentInfo");
            return false;
        }

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        $affectedNow = 0;
        foreach ($rows as $row) {
            foreach ($this->extractDocRows($row) as $docRow) {
                $affectedNow += (bool)$this->importRow($repository, $docRow);
            }
        }
        return $affectedNow;
    }

    protected function extractDocRows(array $row): iterable
    {
        yield $row;
    }

    protected function importRow(BaseRepository $repository, array $row, ?string $docBlob = null): int
    {
        $upserted = 0;
        $docs = $this->findDocEntities($repository, $row);

        if (empty($docs) && $this->modeNoDownload) {
            $docs = [$this->docEntity($repository, $row)];
            $this->log->debug("Skip download {$docs[0]->storagePath()} {$this->documentInfo($docs[0])}");
        }

        // is new document
        if (empty($docs)) {
            $docBlob ??= $this->httpClient->get($this->getDownloadLink($row))->getContent();
            foreach ($this->splitIfPdf($docBlob, $row) as [$newRow, $jpegBlob, $isBroken]) {
                $doc = $this->docEntity($repository, $newRow);

                if ($isBroken) {
                    $doc->external_approve_required = false;
                    $doc->face_processed = true;
                }

                if (!$this->normalizeAndSaveImage($doc, $jpegBlob)) {
                    continue;
                }

                $upserted += (int)($this->upsertUserDocument($repository, $doc) !== DbUpsertResult::NONE);
                $this->log->debug("Add {$doc->storagePath()} $doc->page_num {$this->documentInfo($doc)} Size: " . Str::beautySize(strlen($docBlob)));
            }
        } else {
            foreach ($docs as $doc) {
                $upserted += $curUpdated = (int)($this->upsertUserDocument($repository, $doc) !== DbUpsertResult::NONE);
                $this->log->debug(($curUpdated ? 'Updated' : 'Exist') . " {$doc->storagePath()} $doc->page_num {$this->documentInfo($doc)}");
            }
        }

        return $upserted;
    }

    private function docEntity(BaseRepository $repository, array $row): UserDocument
    {
        /** @var UserDocument $doc */
        $doc = new ($repository::ENTITY_CLASS)($row);
        $doc->updated_at = new \DateTimeImmutable(UserDocument::SQL_NOW_DATETIME);
        $doc->created_by = $doc->updated_by = $this->auth->employeeId();
        return $doc;
    }

    protected function upsertUserDocument(BaseRepository $repository, UserDocument $doc): DbUpsertResult
    {
        if (!isset($doc->id)) {
            $repository->insert($doc);
            return DbUpsertResult::INSERTED;
        }

        if ($doc->hasChangedFields()) {
            $repository->update($doc, $doc->getChangedFields());
            $doc->forgetChangedFields();
            return DbUpsertResult::UPDATED;
        }

        return DbUpsertResult::NONE;
    }

    protected function findDocEntities(BaseRepository $repository, array $row): array
    {
        /** @var UserDocument[] $entities */
        $entities = iterator_to_array($repository->findByQuery(
            (new Query($this->userDocumentsRepo->db))
                ->where(Arr::leaveOnlyKeys($row, ['site_id', 'external_id'], true))
                ->orderBy(['page_num' => SORT_ASC])
        ));

        foreach ($entities as $entity) {
            if (!empty($row['external_updated_at']) && $row['external_updated_at'] != ($entity->external_updated_at ?? null)) {
                $entity->external_updated_at = $row['external_updated_at'];
                $entity->pushChangedFields('external_updated_at');
            }
        }

        return $entities;
    }

    private function saveToStorage(UserDocument $document, \Imagick $imagick): void
    {
        $imageBlob = $imagick->getImageBlob();
        if (empty($imageBlob)) {
            throw new \ImagickException("Imagick empty image blob");
        }

        $uploadedUrl = $this->storage->put($document->storagePath(), $imageBlob, $imagick->getImageMimeType(), [
            'Site' => $document->site_id,
            'User' => $document->user_id,
            'Name' => $document->filename,
            'Tag' => implode(',', $document->tags->toArray()),
            'Date' => date('Y-m-d H:i:s'),
        ]);
        gc_collect_cycles(); // clean garbage that collect s3 storage client

        if ($uploadedUrl === null) {
            throw new \ErrorException("Unable to upload file {$document->storagePath()}");
        }
        $this->lastUploadedDocument = $document;
    }

    protected function beforeSaveFile(UserDocument $document, \Imagick $image): bool
    {
        if (!in_array($image->getImageMimeType(), self::MIME_TYPES_ALLOWED, true)) {
            $this->log->error("Skip {$document->tags[0]} invalid mime: {$image->getImageMimeType()} {$this->documentInfo($document)}");
            return false;
        }

        return true;
    }

    protected function documentInfo(UserDocument $document): string
    {
        if (isset($document->doc_type)) {
            $typeStr = UserDocumentProgress::TYPES_API_NAMES[$document->doc_type];
        } else {
            $typeStr = '[' . implode(', ', $document->tags->toArray()) . ']';
        }
        return "$typeStr $document->external_id";
    }

    public function splitIfPdf(string $content, array $row): iterable
    {
        try {
            $im = new \Imagick();
            // set before load doc to fix pdf low quality, jpeg not affected, default 72x72
            $im->setResolution(144, 144);

            try {
                $im->readImageBlob($content);
            } catch (\Throwable $e) {
                // garbage at beginning of PDF, digital signature?
                $pdfStartPos = strpos(substr($content, 0, 100), '%PDF');
                if ($pdfStartPos === false || $pdfStartPos === 0) {
                    throw $e;
                }
                $im->readImageBlob(substr($content, $pdfStartPos));
            }

            if ($im->getImageMimeType() !== self::MIME_TYPE_PDF) {
                $im->setImageFormat("jpeg");
                yield [$row, $im->getImageBlob(), false];
                return;
            }

            foreach (ImageHelper::splitPdfToJpegs($im) as $fileIndex => $im) {
                $newRow = $row;
                $newRow['filename'] = uniqid('', false) . '.jpg';
                $newRow['page_num'] = $fileIndex;
                yield [$newRow, $im, false];
            }
        } catch (\ImagickException $e) {
            $this->log->warning("Broken image: {$e->getMessage()}");
            yield [$row, $this->getBrokenImageRaw(), true];
        }
    }

    private function getBrokenImageRaw(): string
    {
        if (!is_null($this->rawBrokenImage)) {
            return $this->rawBrokenImage;
        }

        $this->rawBrokenImage = file_get_contents(APP_ROOT . 'web/images/broken_file_pic.png');
        return $this->rawBrokenImage;
    }

    protected function batchSize(): int | callable
    {
        return 1;
    }
}
