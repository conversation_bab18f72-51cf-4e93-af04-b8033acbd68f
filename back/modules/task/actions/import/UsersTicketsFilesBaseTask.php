<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\components\Initializable;
use app\back\components\services\FileStorage;
use app\back\entities\UserTicketFile;
use app\back\modules\task\actions\UsersDocumentsBaseTask;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserTicketFiles;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

abstract class UsersTicketsFilesBaseTask extends ImportTask
{
    public const array MIME_TYPES_ALLOWED = [
        UsersDocumentsBaseTask::MIME_TYPE_JPG => 'jpg',
        UsersDocumentsBaseTask::MIME_TYPE_PNG => 'png',
        UsersDocumentsBaseTask::MIME_TYPE_PDF => 'pdf',
    ];

    protected const string REMOTE_FILE_ID_COLUMN = 'undefined';

    abstract protected function getFilesToDownload(): array;

    protected array $validateOnlyPropNames;

    public function __construct(
        protected readonly ConnectionInterface $db,
        private readonly UserTicketFiles $repository,
        private readonly FileStorage $storage,
    ) {
    }

    #[Initializable]
    final public function init(): void
    {
        $this->validateOnlyPropNames = [static::REMOTE_FILE_ID_COLUMN, 'sync_status', 'upserted_at'];
    }

    protected function getData(): iterable
    {
        foreach ($this->getFilesToDownload() as $file) {
            if ($file['sync_status'] === UserTicketFile::SYNC_FAILED) {
                $this->log->error("Skipping previously failed file with id {$file['fileId']}");
                continue;
            }
            yield $this->createRequest($file)->finalData();
        }
    }

    protected function beforeFind(array &$row): bool
    {
        [$fileContent, $fileId] = [$row['fileContent'], $row['fileId']];
        unset($row['fileContent'], $row['fileId']);

        $row['upserted_at'] = new \DateTimeImmutable();

        if ($row['sync_status'] !== UserTicketFile::SYNC_SUCCESS) {
            return true;
        }

        $mimeType = (new \finfo(FILEINFO_MIME_TYPE))->buffer($fileContent);

        if (!array_key_exists($mimeType, self::MIME_TYPES_ALLOWED)) {
            $this->log->debug("Mime type '$mimeType' isn't allowed in file with id {$fileId}");
            $row['sync_status'] = UserTicketFile::SYNC_INVALID;
            return true;
        }

        $fileExt = self::MIME_TYPES_ALLOWED[$mimeType];
        $this->storage->put($this->repository->getStoragePath($fileId, $fileExt), $fileContent, $mimeType, []);

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpdateDistinct($rows, [], [static::REMOTE_FILE_ID_COLUMN]);
    }

    protected function repository(): BaseRepository
    {
        return $this->repository;
    }

    protected function baseFilesToDownloadQuery(): Query
    {
        return (new Query($this->db))
            ->select([
                'fileId' => 'utf.id',
                'utf.sync_status',
            ])
            ->from(['utf' => UserTicketFiles::TABLE_NAME])
            ->where(['utf.sync_status' => [UserTicketFile::SYNC_TO_DOWNLOAD, UserTicketFile::SYNC_FAILED]]);
    }
}
