<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\DbUpsertResult;
use app\back\components\helpers\UuidHelper;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\modules\task\requests\SmenRequest;
use app\back\repositories\BaseRepository;

class UsersDocumentsSmenTask extends UsersDocumentsBaseTask
{
    use UsersDocumentsWithKycTrait;

    public const string STATUS_AI_VALID = 'ai_valid';
    public const string STATUS_AI_INVALID = 'ai_invalid';
    public const string STATUS_NOT_VERIFIED = 'not_verified';

    private const array AI_VALIDATION_MESSAGES_MAP = [
        'dvs.ai_validation.docs_not_found_on_photo' => UserDocument::AI_V_DOC_NOT_FOUND,
        'dvs.ai_validation.low_doc_probability' => UserDocument::AI_V_LOW_DOC_PROBABILITY,
        'dvs.ai_validation.low_document_quality' => UserDocument::AI_V_LOW_DOC_QUALITY,
        'dvs.ai_validation.low_face_quality' => UserDocument::AI_V_LOW_FACE_QUALITY,
        'dvs.ai_service.error' => UserDocument::AI_V_ERROR,
        'dvs.ai_service.error_response' => UserDocument::AI_V_ERROR_RESPONSE,
        'dvs.ai_service.invalid_response' => UserDocument::AI_V_SERVICE_INVALID_RESPONSE,
        'dvs.ai_service.task_not_found' => UserDocument::AI_V_SERVICE_TASK_NOT_FOUND,
    ];

    private const array TYPES_MAP = [
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD] => [UserDocument::TAG_PASSPORT, UserDocument::TAG_ID_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_DRIVER_LICENSE] => [UserDocument::TAG_DIVING_LICENSE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_INTERNATIONAL_PASSPORT] => [UserDocument::TAG_PASSPORT],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_ID_CARD_BACK] => [UserDocument::TAG_ID_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_ID_CARD_FRONT] => [UserDocument::TAG_ID_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD] => [UserDocument::TAG_SELFIE, UserDocument::TAG_PASSPORT, UserDocument::TAG_ID_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_SELFIE_WITH_DRIVER_LICENSE] => [UserDocument::TAG_SELFIE, UserDocument::TAG_DIVING_LICENSE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_SELFIE_WITH_BANK_CARD] => [UserDocument::TAG_SELFIE, UserDocument::TAG_BANK_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_SELFIE_WITH_IDENTIFICATION_DOCUMENT_AND_BANK_CARD] => [UserDocument::TAG_SELFIE, UserDocument::TAG_ID_CARD, UserDocument::TAG_BANK_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_BANK_STATEMENT] => [UserDocument::TAG_REQUISITE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_BANK_SCREENSHOT] => [UserDocument::TAG_REQUISITE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_BANK_CARD_FRONT] => [UserDocument::TAG_BANK_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_BANK_CARD_BACK] => [UserDocument::TAG_BANK_CARD],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_MOBILE_COMMERCE_CONTRACT] => [UserDocument::TAG_REQUISITE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_WALLET_ACCOUNT_SCREENSHOT] => [UserDocument::TAG_REQUISITE],
        UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_OTHER] => ['other'],
        'passport' => [UserDocument::TAG_PASSPORT],
        'card' => [UserDocument::TAG_BANK_CARD],
        'selfie' => [UserDocument::TAG_SELFIE],
        'bank_statement' => [UserDocument::TAG_REQUISITE],
    ];

    protected function beforeFind(array &$row): bool
    {
        parent::beforeFind($row);

        $row['external_id'] = UuidHelper::cast($row['external_id']);
        $this->downloadUrls[$row['external_id']] = $row['download_url'];
        $row['tags'] = self::TYPES_MAP[$row['type']] ?? [$row['type']];

        $docType = array_search($row['type'], UserDocumentProgress::TYPES_API_NAMES, true);
        if ($docType !== false) {
            $row['doc_type'] = $docType;
        }
        unset($row['type']);

        if (!empty($row['aiStatus']) && $row['aiStatus'] !== self::STATUS_NOT_VERIFIED) {
            $row['ai_validation'] = [
                UserDocument::AI_V_KEY_VALID => match ($row['aiStatus']) {
                    self::STATUS_AI_VALID => true,
                    self::STATUS_AI_INVALID => false,
                },
            ];

            if (!empty($row['aiValidationMessages'])) {
                $row['ai_validation'][UserDocument::AI_V_KEY_MESSAGES] = array_unique(array_map(static fn($msg) => self::AI_VALIDATION_MESSAGES_MAP[$msg], $row['aiValidationMessages']));
            }

            if (!empty($row['aiScheduledAt'])) {
                $row['ai_validation']['scheduledAt'] = $row['aiScheduledAt'];
            }

            if (!empty($row['aiCompletedAt'])) {
                $row['ai_validation']['completedAt'] = $row['aiCompletedAt'];
            }
        }
        unset($row['aiStatus'], $row['aiScheduledAt'], $row['aiCompletedAt'], $row['aiValidationMessages']);

        return true;
    }

    protected function upsertUserDocument(BaseRepository $repository, UserDocument $doc): DbUpsertResult
    {
        return $repository->db->transaction(function () use ($repository, $doc) {
            $upsertResult = parent::upsertUserDocument($repository, $doc);
            if ($upsertResult === DbUpsertResult::INSERTED) {
                $this->updateKycWaitStatus($doc);
            }
            return $upsertResult;
        });
    }

    protected function getDownloadLink(array $row): string
    {
        $urlPath = parse_url($this->downloadUrls[$row['external_id']], PHP_URL_PATH);
        $signature = SmenRequest::urlAndSortedParamsHash($urlPath, $this->request['authKey'], []);
        return "{$this->request['host']}{$urlPath}?signature=$signature";
    }
}
