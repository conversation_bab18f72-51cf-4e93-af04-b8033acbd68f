<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\UuidHelper;

class UsersDocumentsYsTask extends UsersDocumentsBaseTask
{
    protected function beforeFind(array &$row): bool
    {
        parent::beforeFind($row);

        $row['external_id'] = UuidHelper::cast($row['external_id']);
        $row['tags'] = ['unknown'];

        $this->downloadUrls[$row['external_id']] = $row['download_url'];

        return true;
    }

    protected function getDownloadLink(array $row): string
    {
        return $this->downloadUrls[$row['external_id']];
    }
}
