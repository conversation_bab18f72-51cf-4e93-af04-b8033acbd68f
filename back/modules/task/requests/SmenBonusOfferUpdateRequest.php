<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

class SmenBonusOfferUpdateRequest extends SmenRequest
{
    public const string ACTION_ACTIVATE = 'activate';
    public const string ACTION_DEACTIVATE = 'deactivate';
    public const string ACTION_CANCEL = 'cancel';

    public int $bonusOfferId;
    public string $action = self::ACTION_ACTIVATE;

    protected function buildUrl(array $params = []): string
    {
        return parent::buildUrl([
            ':bonusOfferId' => $this->bonusOfferId,
            ':action' => $this->action,
        ]);
    }
}
